C:\Users\<USER>\Desktop\Source Betel\Fighting.Service\bin\Debug\logconfig.xml
C:\Users\<USER>\Desktop\Source Betel\Fighting.Service\bin\Debug\Fighting.Service.exe.config
C:\Users\<USER>\Desktop\Source Betel\Fighting.Service\bin\Debug\Fighting.Service.exe.manifest
C:\Users\<USER>\Desktop\Source Betel\Fighting.Service\bin\Debug\Fighting.Service.application
C:\Users\<USER>\Desktop\Source Betel\Fighting.Service\bin\Debug\Fighting.Service.exe
C:\Users\<USER>\Desktop\Source Betel\Fighting.Service\bin\Debug\Fighting.Service.pdb
C:\Users\<USER>\Desktop\Source Betel\Fighting.Service\obj\Debug\Fighting.Service.csproj.AssemblyReference.cache
C:\Users\<USER>\Desktop\Source Betel\Fighting.Service\obj\Debug\Fighting.Service.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Desktop\Source Betel\Fighting.Service\obj\Debug\Fighting.Service.csproj.Fody.CopyLocal.cache
C:\Users\<USER>\Desktop\Source Betel\Fighting.Service\obj\Debug\Fighting.Service.exe.manifest
C:\Users\<USER>\Desktop\Source Betel\Fighting.Service\obj\Debug\Fighting.Service.application
C:\Users\<USER>\Desktop\Source Betel\Fighting.Service\obj\Debug\Fighting.Service.exe
C:\Users\<USER>\Desktop\Source Betel\Fighting.Service\obj\Debug\Fighting.Service.pdb
C:\Users\<USER>\Desktop\Source\Fighting.Service\obj\Debug\Fighting.Service.csproj.AssemblyReference.cache
C:\Users\<USER>\Desktop\Source\Fighting.Service\obj\Debug\Fighting.Service.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Desktop\Source\Fighting.Service\obj\Debug\Fighting.Service.exe
C:\Users\<USER>\Desktop\Source\Fighting.Service\obj\Debug\Fighting.Service.pdb
D:\DDTANK PRISMA\Source\Source\Fighting.Service\bin\Debug\logconfig.xml
D:\DDTANK PRISMA\Source\Source\Fighting.Service\bin\Debug\Fighting.Service.exe.config
D:\DDTANK PRISMA\Source\Source\Fighting.Service\bin\Debug\Fighting.Service.exe.manifest
D:\DDTANK PRISMA\Source\Source\Fighting.Service\bin\Debug\Fighting.Service.application
D:\DDTANK PRISMA\Source\Source\Fighting.Service\bin\Debug\Fighting.Service.exe
D:\DDTANK PRISMA\Source\Source\Fighting.Service\bin\Debug\Fighting.Service.pdb
D:\DDTANK PRISMA\Source\Source\Fighting.Service\obj\Debug\Fighting.Service.csproj.AssemblyReference.cache
D:\DDTANK PRISMA\Source\Source\Fighting.Service\obj\Debug\Fighting.Service.csproj.CoreCompileInputs.cache
D:\DDTANK PRISMA\Source\Source\Fighting.Service\obj\Debug\Fighting.Service.csproj.Fody.CopyLocal.cache
D:\DDTANK PRISMA\Source\Source\Fighting.Service\obj\Debug\Fighting.Service.csproj.Fody.RuntimeCopyLocal.cache
D:\DDTANK PRISMA\Source\Source\Fighting.Service\obj\Debug\Fighting.Service.exe.manifest
D:\DDTANK PRISMA\Source\Source\Fighting.Service\obj\Debug\Fighting.Service.application
D:\DDTANK PRISMA\Source\Source\Fighting.Service\obj\Debug\Fighting.Service.exe
D:\DDTANK PRISMA\Source\Source\Fighting.Service\obj\Debug\Fighting.Service.pdb
C:\Source\Source\Fighting.Service\bin\Debug\logconfig.xml
C:\Source\Source\Fighting.Service\bin\Debug\Fighting.Service.exe.config
C:\Source\Source\Fighting.Service\bin\Debug\Fighting.Service.exe.manifest
C:\Source\Source\Fighting.Service\bin\Debug\Fighting.Service.application
C:\Source\Source\Fighting.Service\bin\Debug\Fighting.Service.exe
C:\Source\Source\Fighting.Service\bin\Debug\Fighting.Service.pdb
C:\Source\Source\Fighting.Service\obj\Debug\Fighting.Service.csproj.AssemblyReference.cache
C:\Source\Source\Fighting.Service\obj\Debug\Fighting.Service.csproj.CoreCompileInputs.cache
C:\Source\Source\Fighting.Service\obj\Debug\Fighting.Service.exe.manifest
C:\Source\Source\Fighting.Service\obj\Debug\Fighting.Service.application
C:\Source\Source\Fighting.Service\obj\Debug\Fighting.Service.exe
C:\Source\Source\Fighting.Service\obj\Debug\Fighting.Service.pdb
C:\Source\Source\Fighting.Service\obj\Debug\Fighting.Service.csproj.Fody.CopyLocal.cache
C:\Source\Source\Fighting.Service\obj\Debug\Fighting.Service.csproj.Fody.RuntimeCopyLocal.cache
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\logconfig.xml
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\Fighting.Service.exe.config
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\Fighting.Service.exe.manifest
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\Fighting.Service.application
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\Fighting.Service.exe
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\Fighting.Service.pdb
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\obj\Debug\Fighting.Service.csproj.AssemblyReference.cache
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\obj\Debug\Fighting.Service.csproj.CoreCompileInputs.cache
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\obj\Debug\Fighting.Service.exe.manifest
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\obj\Debug\Fighting.Service.application
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\obj\Debug\Fighting.Service.exe
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\obj\Debug\Fighting.Service.pdb
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\obj\Debug\Fighting.8112A6BA.Up2Date
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\Newtonsoft.Json.xml
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\Costura.dll
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\Fighting.Server.dll
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\Game.Base.dll
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\Game.Logic.dll
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\log4net.dll
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\Newtonsoft.Json.dll
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\SqlDataProvider.dll
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\Bussiness.dll
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\zlib.net.dll
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\protobuf-net.dll
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\protobuf-net.Core.dll
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\System.Memory.dll
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\System.Buffers.dll
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\System.Numerics.Vectors.dll
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\Fighting.Server.pdb
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\Game.Base.pdb
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\Game.Logic.pdb
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\SqlDataProvider.pdb
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\Costura.pdb
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\Costura.xml
D:\.aDDTPrismaRafa\Source-emulador\Fighting.Service\bin\Debug\Bussiness.pdb
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\logconfig.xml
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Service.exe.config
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Service.exe.manifest
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Service.application
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Service.exe
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Service.pdb
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Newtonsoft.Json.xml
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.Service.csproj.AssemblyReference.cache
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.Service.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.Service.exe.manifest
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.Service.application
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.8112A6BA.Up2Date
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.Service.exe
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.Service.pdb
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\logconfig.xml
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Service.exe.config
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Service.exe.manifest
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Service.application
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Service.exe
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Service.pdb
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Newtonsoft.Json.xml
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.Service.csproj.AssemblyReference.cache
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.Service.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.Service.exe.manifest
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.Service.application
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.8112A6BA.Up2Date
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.Service.exe
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.Service.pdb
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Costura.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Server.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Game.Base.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Game.Logic.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\log4net.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Newtonsoft.Json.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\SqlDataProvider.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Bussiness.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\zlib.net.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\protobuf-net.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\protobuf-net.Core.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\System.Memory.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\System.Buffers.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\System.Numerics.Vectors.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Server.pdb
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Game.Base.pdb
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Game.Logic.pdb
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\SqlDataProvider.pdb
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Costura.pdb
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Costura.xml
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Bussiness.pdb
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.Service.csproj.AssemblyReference.cache
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.Service.exe.config
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.Service.csproj.CoreCompileInputs.cache
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.Service.exe
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.Service.pdb
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\logconfig.xml
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Service.exe.config
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Service.exe.manifest
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Service.application
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Service.exe
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Service.pdb
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Newtonsoft.Json.xml
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.Service.exe.manifest
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.Service.application
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.8112A6BA.Up2Date
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Costura.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Server.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Game.Base.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Game.Logic.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\log4net.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Newtonsoft.Json.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\SqlDataProvider.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Bussiness.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\zlib.net.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\protobuf-net.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\protobuf-net.Core.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\System.Memory.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\System.Buffers.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\System.Numerics.Vectors.dll
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Server.pdb
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Game.Base.pdb
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Game.Logic.pdb
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\SqlDataProvider.pdb
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Costura.pdb
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Costura.xml
C:\Users\<USER>\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Bussiness.pdb
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Costura.dll
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Server.dll
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Game.Base.dll
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Game.Logic.dll
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\log4net.dll
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Newtonsoft.Json.dll
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\SqlDataProvider.dll
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Bussiness.dll
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\zlib.net.dll
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\protobuf-net.dll
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\protobuf-net.Core.dll
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\System.Memory.dll
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\System.Buffers.dll
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\System.Numerics.Vectors.dll
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Server.pdb
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Game.Base.pdb
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Game.Logic.pdb
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\SqlDataProvider.pdb
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Costura.pdb
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Costura.xml
C:\Users\<USER>\OneDrive\Documents\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Bussiness.pdb
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\logconfig.xml
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Service.exe.config
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Service.exe.manifest
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Service.application
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Service.exe
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Service.pdb
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Newtonsoft.Json.xml
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.Service.csproj.AssemblyReference.cache
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.Service.exe.config
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.Service.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.Service.exe.manifest
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.Service.application
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.8112A6BA.Up2Date
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.Service.exe
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\obj\Debug\Fighting.Service.pdb
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Costura.dll
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Server.dll
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Game.Base.dll
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Game.Logic.dll
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\log4net.dll
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Newtonsoft.Json.dll
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\SqlDataProvider.dll
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Bussiness.dll
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\zlib.net.dll
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\protobuf-net.dll
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\protobuf-net.Core.dll
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\System.Memory.dll
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\System.Buffers.dll
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\System.Numerics.Vectors.dll
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Fighting.Server.pdb
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Game.Base.pdb
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Game.Logic.pdb
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\SqlDataProvider.pdb
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Costura.pdb
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Costura.xml
C:\Users\<USER>\Downloads\GitHub\GitHub\DDTankPrisma\Fighting.Service\bin\Debug\Bussiness.pdb
D:\SourceBetel\Fighting.Service\bin\Debug\logconfig.xml
D:\SourceBetel\Fighting.Service\bin\Debug\Fighting.Service.exe.config
D:\SourceBetel\Fighting.Service\bin\Debug\Fighting.Service.exe.manifest
D:\SourceBetel\Fighting.Service\bin\Debug\Fighting.Service.application
D:\SourceBetel\Fighting.Service\bin\Debug\Fighting.Service.exe
D:\SourceBetel\Fighting.Service\bin\Debug\Fighting.Service.pdb
D:\SourceBetel\Fighting.Service\bin\Debug\Newtonsoft.Json.xml
D:\SourceBetel\Fighting.Service\obj\Debug\Fighting.Service.csproj.AssemblyReference.cache
D:\SourceBetel\Fighting.Service\obj\Debug\Fighting.Service.exe.config
D:\SourceBetel\Fighting.Service\obj\Debug\Fighting.Service.csproj.CoreCompileInputs.cache
D:\SourceBetel\Fighting.Service\obj\Debug\Fighting.Service.csproj.Fody.CopyLocal.cache
D:\SourceBetel\Fighting.Service\obj\Debug\Fighting.Service.csproj.Fody.RuntimeCopyLocal.cache
D:\SourceBetel\Fighting.Service\obj\Debug\Fighting.Service.exe.manifest
D:\SourceBetel\Fighting.Service\obj\Debug\Fighting.Service.application
D:\SourceBetel\Fighting.Service\obj\Debug\Fighting.8112A6BA.Up2Date
D:\SourceBetel\Fighting.Service\obj\Debug\Fighting.Service.exe
D:\SourceBetel\Fighting.Service\obj\Debug\Fighting.Service.pdb
D:\DDT55Amorim\New\SourceBetel\Fighting.Service\bin\Debug\logconfig.xml
D:\DDT55Amorim\New\SourceBetel\Fighting.Service\bin\Debug\Fighting.Service.exe.config
D:\DDT55Amorim\New\SourceBetel\Fighting.Service\bin\Debug\Fighting.Service.exe.manifest
D:\DDT55Amorim\New\SourceBetel\Fighting.Service\bin\Debug\Fighting.Service.application
D:\DDT55Amorim\New\SourceBetel\Fighting.Service\bin\Debug\Fighting.Service.exe
D:\DDT55Amorim\New\SourceBetel\Fighting.Service\bin\Debug\Fighting.Service.pdb
D:\DDT55Amorim\New\SourceBetel\Fighting.Service\bin\Debug\Newtonsoft.Json.xml
D:\DDT55Amorim\New\SourceBetel\Fighting.Service\obj\Debug\Fighting.Service.csproj.AssemblyReference.cache
D:\DDT55Amorim\New\SourceBetel\Fighting.Service\obj\Debug\Fighting.Service.csproj.CoreCompileInputs.cache
D:\DDT55Amorim\New\SourceBetel\Fighting.Service\obj\Debug\Fighting.Service.csproj.Fody.CopyLocal.cache
D:\DDT55Amorim\New\SourceBetel\Fighting.Service\obj\Debug\Fighting.Service.csproj.Fody.RuntimeCopyLocal.cache
D:\DDT55Amorim\New\SourceBetel\Fighting.Service\obj\Debug\Fighting.Service.exe.manifest
D:\DDT55Amorim\New\SourceBetel\Fighting.Service\obj\Debug\Fighting.Service.application
D:\DDT55Amorim\New\SourceBetel\Fighting.Service\obj\Debug\Fighting.8112A6BA.Up2Date
D:\DDT55Amorim\New\SourceBetel\Fighting.Service\obj\Debug\Fighting.Service.exe
D:\DDT55Amorim\New\SourceBetel\Fighting.Service\obj\Debug\Fighting.Service.pdb
