package playerDress.views
{
   import bagAndInfo.bag.BagListView;
   import bagAndInfo.cell.BagCell;
   import bagAndInfo.cell.BaseCell;
   import ddt.data.BagInfo;
   import ddt.data.goods.InventoryItemInfo;
   import ddt.events.BagEvent;
   import ddt.manager.SocketManager;
   import flash.utils.ByteArray;
   import flash.utils.Dictionary;
   import playerDress.PlayerDressManager;
   import playerDress.components.DressUtils;
   
   public class DressBagListView extends BagListView
   {
      public static const DRESS_INDEX:int = 80;
      
      private var _dressType:int;
      
      private var _sex:int;
      
      private var _searchStr:String;
      
      private var _displayItems:Dictionary;
      
      private var _equipBag:BagInfo;
      
      private var _virtualBag:BagInfo;
      
      private var _currentPage:int = 1;
      
      public var locked:Boolean = false;
      
      public function DressBagListView(param1:int, param2:int = 7, param3:int = 49)
      {
         super(param1,param2,param3);
      }
      
      public function setSortType(param1:int, param2:Boolean, param3:String = "") : void
      {
         this._dressType = param1;
         this._sex = param2 ? int(1) : int(2);
         this._searchStr = param3;
         this.sortItems();
      }
      
      override public function setData(param1:BagInfo) : void
      {
         if(this._equipBag != null)
         {
            this._equipBag.removeEventListener(BagEvent.UPDATE,this.__updateGoods);
         }
         this._equipBag = param1;
         this.setVirtualBagData();
         this._equipBag.addEventListener(BagEvent.UPDATE,this.__updateGoods);
      }
      
      private function setVirtualBagData() : void
      {
         var _loc1_:InventoryItemInfo = null;
         this._virtualBag = new BagInfo(BagInfo.EQUIPBAG,48);
         var _loc2_:int = 0;
         for each(_loc1_ in this._equipBag.items)
         {
            if(DressUtils.isDress(_loc1_))
            {
               this._virtualBag.items[_loc2_] = _loc1_;
               _loc2_++;
            }
         }
      }
      
      override protected function __updateGoods(param1:BagEvent) : void
      {
         if(!this.locked)
         {
            this.setVirtualBagData();
            this.sortItems();
         }
      }
      
      private function sortItems() : void
      {
         var _loc1_:* = null;
         var _loc2_:InventoryItemInfo = null;
         this._displayItems = new Dictionary();
         clearDataCells();
         var _loc3_:int = 0;
         this.sequenceItems();
         for(_loc1_ in this._virtualBag.items)
         {
            _loc2_ = this._virtualBag.items[_loc1_];
            if(_loc2_.NeedSex == this._sex || _loc2_.NeedSex == 0)
            {
               if(!PlayerDressManager.instance.showBind)
               {
                  if(_loc2_.IsBinds == true)
                  {
                     continue;
                  }
               }
               if(this._dressType == -1)
               {
                  if(this._searchStr != "" && _loc2_.Name.indexOf(this._searchStr) != -1)
                  {
                     this._displayItems[_loc3_] = _loc2_;
                     if(_loc3_ >= 0 && _loc3_ < _cellNum)
                     {
                        BaseCell(_cells[_loc3_]).info = _loc2_;
                     }
                     _loc3_++;
                  }
               }
               else if(this._dressType == 0)
               {
                  this._displayItems[_loc3_] = _loc2_;
                  if(_loc3_ >= 0 && _loc3_ < _cellNum)
                  {
                     BaseCell(_cells[_loc3_]).info = _loc2_;
                  }
                  _loc3_++;
               }
               else if(_loc2_.CategoryID == this._dressType)
               {
                  this._displayItems[_loc3_] = _loc2_;
                  if(_loc3_ >= 0 && _loc3_ < _cellNum)
                  {
                     BaseCell(_cells[_loc3_]).info = _loc2_;
                  }
                  _loc3_++;
               }
            }
         }
         this._currentPage = 1;
         (parent as DressBagView).currentPage = 1;
         (parent as DressBagView).updatePage();
      }
      
      private function sequenceItems() : void
      {
         var _loc1_:Array = null;
         var _loc2_:ByteArray = null;
         _loc1_ = null;
         var _loc3_:InventoryItemInfo = null;
         _loc2_ = null;
         var _loc4_:InventoryItemInfo = null;
         var _loc5_:BagInfo = new BagInfo(BagInfo.EQUIPBAG,48);
         _loc1_ = [];
         var _loc6_:Array = [];
         for each(_loc3_ in this._virtualBag.items)
         {
            _loc1_.push({
               "TemplateID":_loc3_.TemplateID,
               "ItemID":_loc3_.ItemID,
               "CategoryIDSort":DressUtils.getBagGoodsCategoryIDSort(uint(_loc3_.CategoryID)),
               "Place":_loc3_.Place,
               "RemainDate":_loc3_.getRemainDate() > 0,
               "CanStrengthen":_loc3_.CanStrengthen,
               "StrengthenLevel":_loc3_.StrengthenLevel,
               "IsBinds":_loc3_.IsBinds
            });
         }
         _loc2_ = new ByteArray();
         _loc2_.writeObject(_loc1_);
         _loc2_.position = 0;
         _loc6_ = _loc2_.readObject() as Array;
         _loc1_.sortOn(["RemainDate","CategoryIDSort","TemplateID","CanStrengthen","IsBinds","StrengthenLevel","Place"],[Array.DESCENDING,Array.NUMERIC,Array.NUMERIC | Array.DESCENDING,Array.DESCENDING,Array.DESCENDING,Array.NUMERIC | Array.DESCENDING,Array.NUMERIC]);
         if(this.bagComparison(_loc1_,_loc6_))
         {
            return;
         }
         var _loc7_:int = 0;
         var _loc8_:int = 0;
         while(_loc8_ <= _loc1_.length - 1)
         {
            for each(_loc4_ in this._virtualBag.items)
            {
               if(_loc1_[_loc8_].Place == _loc4_.Place)
               {
                  _loc5_.items[_loc7_] = _loc4_;
                  _loc7_++;
                  break;
               }
            }
            _loc8_++;
         }
         this._virtualBag = _loc5_;
      }
      
      private function bagComparison(param1:Array, param2:Array) : Boolean
      {
         if(param1.length < param2.length)
         {
            return false;
         }
         var _loc3_:int = int(param1.length);
         var _loc4_:int = 0;
         while(_loc4_ < _loc3_)
         {
            if(param1[_loc4_].ItemID != param2[_loc4_].ItemID || param1[_loc4_].TemplateID != param2[_loc4_].TemplateID)
            {
               return false;
            }
            _loc4_++;
         }
         return true;
      }
      
      public function foldItems() : void
      {
         var _loc1_:* = null;
         var _loc2_:InventoryItemInfo = null;
         var _loc3_:Boolean = false;
         var _loc4_:* = null;
         var _loc5_:int = 0;
         var _loc6_:int = 0;
         var _loc7_:Dictionary = new Dictionary();
         var _loc8_:Array = [];
         for(_loc1_ in this._virtualBag.items)
         {
            _loc2_ = this._virtualBag.items[_loc1_];
            if(DressUtils.isDress(_loc2_) && DressUtils.hasNoAddition(_loc2_))
            {
               _loc3_ = true;
               for(_loc4_ in _loc7_)
               {
                  if(_loc2_.TemplateID == parseInt(_loc4_))
                  {
                     _loc5_ = int(_loc7_[_loc4_]);
                     _loc6_ = int(this._virtualBag.items[_loc5_].Place);
                     _loc8_.push({
                        "sPlace":_loc6_,
                        "tPlace":_loc2_.Place
                     });
                     _loc3_ = false;
                     break;
                  }
               }
               if(_loc3_)
               {
                  _loc7_[_loc2_.TemplateID] = _loc1_;
               }
            }
         }
         this._equipBag.isBatch = true;
         SocketManager.Instance.out.foldDressItem(_loc8_);
      }
      
      public function fillPage(param1:int) : void
      {
         var _loc2_:* = null;
         var _loc3_:int = 0;
         this._currentPage = param1;
         clearDataCells();
         for(_loc2_ in this._displayItems)
         {
            _loc3_ = parseInt(_loc2_) - (this._currentPage - 1) * _cellNum;
            if(_loc3_ >= 0 && _loc3_ < _cellNum)
            {
               BaseCell(_cells[_loc3_]).info = this._displayItems[_loc2_];
            }
         }
      }
      
      public function displayItemsLength() : int
      {
         var _loc1_:* = undefined;
         var _loc2_:int = 0;
         for each(_loc1_ in this._displayItems)
         {
            _loc2_++;
         }
         return _loc2_;
      }
      
      private function _cellsSort(param1:Array) : void
      {
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         var _loc5_:Number = NaN;
         var _loc2_:BagCell = null;
         _loc3_ = 0;
         _loc4_ = 0;
         _loc2_ = null;
         _loc3_ = 0;
         _loc5_ = NaN;
         _loc4_ = 0;
         _loc2_ = null;
         _loc3_ = 0;
         var _loc6_:Number = NaN;
         _loc5_ = NaN;
         _loc4_ = 0;
         _loc2_ = null;
         if(param1.length <= 0)
         {
            return;
         }
         _loc3_ = 0;
         while(_loc3_ < param1.length)
         {
            _loc6_ = Number(param1[_loc3_].x);
            _loc5_ = Number(param1[_loc3_].y);
            _loc4_ = int(_cellVec.indexOf(param1[_loc3_]));
            _loc2_ = _cellVec[_loc3_];
            param1[_loc3_].x = _loc2_.x;
            param1[_loc3_].y = _loc2_.y;
            _loc2_.x = _loc6_;
            _loc2_.y = _loc5_;
            _cellVec[_loc3_] = param1[_loc3_];
            _cellVec[_loc4_] = _loc2_;
            _loc3_++;
         }
      }
      
      public function unlockBag() : void
      {
         this.setVirtualBagData();
         this.sortItems();
         this.locked = false;
      }
      
      override public function dispose() : void
      {
         this.locked = false;
         if(Boolean(this._equipBag))
         {
            this._equipBag.removeEventListener(BagEvent.UPDATE,this.__updateGoods);
         }
         super.dispose();
      }
   }
}

