using System;
using System.Linq;

namespace SqlDataProvider.Data
{
	public class UserAvatarColectionInfo : DataObject
	{
		private int _ID;

		private int _userID;

		private DateTime _endTime;

		private int _dataId;

		private int _activeCount;

		private int _sex;

		private string _activeDress;

		public int ID
		{
			get
			{
				return _ID;
			}
			set
			{
				_ID = value;
				_isDirty = true;
			}
		}

		public int UserID
		{
			get
			{
				return _userID;
			}
			set
			{
				_userID = value;
				_isDirty = true;
			}
		}

		public DateTime EndTime
		{
			get
			{
				return _endTime;
			}
			set
			{
				_endTime = value;
				_isDirty = true;
			}
		}

		public bool IsValidate => _endTime >= DateTime.Now;

		public int DataId
		{
			get
			{
				return _dataId;
			}
			set
			{
				_dataId = value;
				_isDirty = true;
			}
		}

		public int ActiveCount
		{
			get
			{
				return _activeCount;
			}
			set
			{
				_activeCount = value;
				_isDirty = true;
			}
		}

		public int Sex
		{
			get
			{
				return _sex;
			}
			set
			{
				_sex = value;
				_isDirty = true;
			}
		}

		public string ActiveDress
		{
			get
			{
				return ConvertArray();
			}
			set
			{
				_activeDress = value;
				_isDirty = true;
			}
		}

		public string[] CurrentGroup => string.IsNullOrEmpty(_activeDress) ? new string[0] : _activeDress.Split(',');

		public string ConvertArray()
		{
			return string.IsNullOrEmpty(_activeDress) ? "" : CurrentGroup.Aggregate("", (string current, string id) => current + "," + id).Substring(1);
		}

		public bool HaftActive()
		{
			return CurrentGroup.Length >= ActiveCount / 2;
		}

		public bool FullActive()
		{
			return CurrentGroup.Length >= ActiveCount;
		}
	}
}
