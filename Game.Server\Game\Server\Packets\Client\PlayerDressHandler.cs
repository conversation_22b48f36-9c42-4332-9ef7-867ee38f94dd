using System;
using Game.Base.Packets;
using Game.Server.GameUtils;
using SqlDataProvider.Data;

namespace Game.Server.Packets.Client
{
	[PacketHandler(237, "更新征婚信息")]
	public class PlayerDressHandler : IPacketHandler
	{
		public int HandlePacket(GameClient client, GSPacketIn packet)
		{
			byte b = packet.ReadByte();
			switch (b)
			{
			case 1:
			{
				int num3 = packet.ReadInt();
				int num4 = packet.ReadInt();
				if (num4 < 0 || num3 < 0 || num3 > 2)
				{
					client.Player.SendMessage("<PERSON><PERSON>y ra lỗi khi kiểm tra dữ liệu client.");
					return 0;
				}
				client.Player.DressModel.ClearDressInSlot(num3);
				for (int j = 0; j < num4; j++)
				{
					int bageType3 = packet.ReadInt();
					int slot3 = packet.ReadInt();
					ItemInfo itemInfo = null;
					PlayerInventory inventory3 = client.Player.GetInventory((eBageType)bageType3);
					if (inventory3 != null)
					{
						itemInfo = inventory3.GetItemAt(slot3);
					}
					if (itemInfo != null)
					{
						UserDressModelInfo userDressModelInfo = new UserDressModelInfo();
						userDressModelInfo.UserID = client.Player.PlayerCharacter.ID;
						userDressModelInfo.ItemID = itemInfo.ItemID;
						userDressModelInfo.CategoryID = itemInfo.Template.CategoryID;
						userDressModelInfo.SlotID = num3;
						userDressModelInfo.TemplateID = itemInfo.TemplateID;
						client.Player.DressModel.AddDressModel(userDressModelInfo);
					}
					else
					{
						client.Player.SendMessage("Atualize...Porfavor...");
					}
				}
				client.Out.SendDressModelInfo(client.Player.DressModel);
				return 0;
			}
			case 2:
			{
				int num2 = packet.ReadInt();
				if (num2 >= 0 && num2 < 3)
				{
					client.Player.PlayerCharacter.CurrentDressModel = num2;
					client.Out.SendCurrentDressModel(client.Player.PlayerCharacter);
					return 0;
				}
				client.Player.SendMessage("Không tồn tại kiểu này.");
				return 0;
			}
			case 6:
			{
				int num = packet.ReadInt();
				for (int i = 0; i < num; i++)
				{
					int bageType = packet.ReadInt();
					int slot = packet.ReadInt();
					int bageType2 = packet.ReadInt();
					int slot2 = packet.ReadInt();
					PlayerInventory inventory = client.Player.GetInventory((eBageType)bageType);
					PlayerInventory inventory2 = client.Player.GetInventory((eBageType)bageType2);
					if (inventory == null || inventory2 == null)
					{
						return 0;
					}
					ItemInfo itemAt = inventory.GetItemAt(slot);
					ItemInfo itemAt2 = inventory2.GetItemAt(slot2);
					if (itemAt == null || itemAt2 == null || itemAt.TemplateID != itemAt2.TemplateID || itemAt == itemAt2)
					{
						continue;
					}
					if (itemAt2.ValidDate != 0)
					{
						if (itemAt.ValidDate != 0)
						{
							itemAt2.ValidDate += itemAt.ValidDate;
						}
						else
						{
							itemAt2.ValidDate = itemAt.ValidDate;
						}
					}
					if (itemAt.IsUsed)
					{
						itemAt2.IsUsed = true;
					}
					inventory2.UpdateItem(itemAt2);
					inventory.RemoveItem(itemAt);
				}
				return 0;
			}
			case 7:
				return 0;
			default:
				Console.WriteLine("playerdress cmd: " + b);
				return 0;
			}
		}

		private void SendCurrentModel(GameClient client, GSPacketIn packet)
		{
			GSPacketIn gSPacketIn = new GSPacketIn(237);
			gSPacketIn.WriteByte(2);
		}
	}
}
