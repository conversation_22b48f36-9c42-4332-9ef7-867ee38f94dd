using System.Collections.Generic;
using System.Reflection;
using Bussiness;
using Game.Server.GameObjects;
using log4net;
using SqlDataProvider.Data;

namespace Game.Server.GameUtils
{
	public class PlayerDressModel
	{
		private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		protected object m_lock = new object();

		protected GamePlayer m_player;

		private List<UserDressModelInfo> m_dressmodel;

		private bool m_saveToDb;

		public GamePlayer Player => m_player;

		public List<UserDressModelInfo> DressModel
		{
			get
			{
				return m_dressmodel;
			}
			set
			{
				m_dressmodel = value;
			}
		}

		public PlayerDressModel(GamePlayer player, bool saveTodb)
		{
			m_player = player;
			m_saveToDb = saveTodb;
			m_dressmodel = new List<UserDressModelInfo>();
		}

		public virtual void LoadFromDatabase()
		{
			if (!m_saveToDb)
			{
				return;
			}
			using (PlayerBussiness playerBussiness = new PlayerBussiness())
			{
				List<UserDressModelInfo> allDressModel = playerBussiness.GetAllDressModel(Player.PlayerCharacter.ID);
				if (allDressModel.Count <= 0)
				{
					return;
				}
				foreach (UserDressModelInfo item in allDressModel)
				{
					AddDressInfo(item);
				}
			}
		}

		public void AddDressInfo(UserDressModelInfo info)
		{
			lock (m_dressmodel)
			{
				m_dressmodel.Add(info);
			}
		}

		public virtual List<UserDressModelInfo> GetDressModelWithSlotID(int slotID)
		{
			lock (m_dressmodel)
			{
				List<UserDressModelInfo> list = new List<UserDressModelInfo>();
				if (m_dressmodel.Count > 0)
				{
					foreach (UserDressModelInfo item in m_dressmodel)
					{
						if (item.SlotID == slotID && !item.IsDelete)
						{
							list.Add(item);
						}
					}
				}
				return list;
			}
		}

		public virtual List<UserDressModelInfo> GetDressModel(int slotID, int catId)
		{
			lock (m_dressmodel)
			{
				List<UserDressModelInfo> list = new List<UserDressModelInfo>();
				if (m_dressmodel.Count > 0)
				{
					foreach (UserDressModelInfo item in m_dressmodel)
					{
						if (item.SlotID == slotID && item.CategoryID == catId && !item.IsDelete)
						{
							list.Add(item);
						}
					}
				}
				return list;
			}
		}

		public virtual void AddDressModel(UserDressModelInfo dress)
		{
			lock (m_dressmodel)
			{
				List<UserDressModelInfo> dressModel = GetDressModel(dress.SlotID, dress.CategoryID);
				if (dressModel.Count > 0)
				{
					foreach (UserDressModelInfo item in dressModel)
					{
						RemoveDressModel(item);
					}
				}
				dress.IsDelete = false;
				m_dressmodel.Add(dress);
			}
		}

		public virtual bool RemoveDressModel(UserDressModelInfo dress)
		{
			bool result;
			lock (m_dressmodel)
			{
				foreach (UserDressModelInfo item in m_dressmodel)
				{
					if (item == dress)
					{
						item.IsDelete = true;
						result = true;
						return result;
					}
				}
				result = false;
			}
			return result;
		}

		public virtual bool ClearDressInSlot(int slotid)
		{
			lock (m_dressmodel)
			{
				foreach (UserDressModelInfo item in m_dressmodel)
				{
					if (item.SlotID == slotid)
					{
						item.IsDelete = true;
					}
				}
				return true;
			}
		}

		public virtual void SaveToDatabase()
		{
			if (!m_saveToDb)
			{
				return;
			}
			using (PlayerBussiness playerBussiness = new PlayerBussiness())
			{
				lock (m_lock)
				{
					for (int i = 0; i < m_dressmodel.Count; i++)
					{
						UserDressModelInfo userDressModelInfo = m_dressmodel[i];
						if (userDressModelInfo == null || !userDressModelInfo.IsDirty)
						{
							continue;
						}
						if (userDressModelInfo.ID > 0 && !userDressModelInfo.IsDelete)
						{
							playerBussiness.UpdateUserDressModel(userDressModelInfo);
						}
						else if (userDressModelInfo.ID <= 0 && !userDressModelInfo.IsDelete)
						{
							playerBussiness.AddUserDressModel(userDressModelInfo);
						}
						else if (userDressModelInfo.IsDelete)
						{
							if (userDressModelInfo.ID > 0)
							{
								playerBussiness.DeleteUserDressModel(userDressModelInfo);
							}
							m_dressmodel.Remove(userDressModelInfo);
						}
					}
				}
			}
		}
	}
}
