using System;
using System.Collections.Generic;
using System.Linq;
using Business.Managers;
using Bussiness;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.GameUtils
{
	public class AvatarCollectionInventory
	{
		protected object m_lock = new object();

		protected GamePlayer m_player;

		private int m_currentModelIndex;

		private Dictionary<int, List<ItemInfo>> m_dressModelArr;

		private Dictionary<int, UserAvatarColectionInfo> m_avatarColect;

		public GamePlayer Player => m_player;

		public Dictionary<int, List<ItemInfo>> DressModelArr => m_dressModelArr;

		public int CurrentModelIndex
		{
			get
			{
				return m_currentModelIndex;
			}
			set
			{
				m_currentModelIndex = ((value > 5) ? 5 : value);
			}
		}

		public Dictionary<int, UserAvatarColectionInfo> AvatarColect => m_avatarColect;

		public AvatarCollectionInventory(GamePlayer player)
		{
			m_player = player;
			m_dressModelArr = new Dictionary<int, List<ItemInfo>>
			{
				[0] = new List<ItemInfo>(),
				[1] = new List<ItemInfo>(),
				[2] = new List<ItemInfo>()
			};
			m_avatarColect = new Dictionary<int, UserAvatarColectionInfo>();
		}

		public virtual void LoadFromDatabase()
		{
			using (PlayerBussiness playerBussiness = new PlayerBussiness())
			{
				UserAvatarColectionInfo[] avatarColectionInfo = playerBussiness.GetSingleUserAvatarColectionInfo(Player.PlayerCharacter.ID);
				if (avatarColectionInfo.Length != 0)
				{
					UserAvatarColectionInfo[] array = avatarColectionInfo;
					foreach (UserAvatarColectionInfo info in array)
					{
						AddAvatarColectionInfo(info.DataId, info.Sex, info);
					}
				}
				CheckNewAvatarColection();
			}
		}

		public virtual void SaveToDatabase()
		{
			using (PlayerBussiness playerBussiness = new PlayerBussiness())
			{
				lock (m_lock)
				{
					foreach (UserAvatarColectionInfo info in m_avatarColect.Values)
					{
						if (info != null && info.IsDirty)
						{
							if (info.ID > 0)
							{
								playerBussiness.UpdateUserAvatarColectionInfo(info);
							}
							else
							{
								playerBussiness.AddUserAvatarColectionInfo(info);
							}
						}
					}
				}
			}
		}

		public void UpdateInfo()
		{
			UpdateCurrentDressModels();
			AvatarColectionInfoChange();
			Player.Out.SendAvatarUpdateProperty(Player);
		}

		public int CountInGroup(int groupId)
		{
			return AvatarCollectionMgr.FindClothGroupTemplateInfo(groupId).Count;
		}

		public void CheckNewAvatarColection()
		{
			foreach (ClothPropertyTemplateInfo propertyTemplateInfo in AvatarCollectionMgr.GetClothPropertyTemplateInfos())
			{
				if (!AvatarColect.ContainsKey(propertyTemplateInfo.ID))
				{
					UserAvatarColectionInfo info = new UserAvatarColectionInfo
					{
						UserID = m_player.PlayerCharacter.ID,
						EndTime = DateTime.Now.AddDays(-1.0),
						DataId = propertyTemplateInfo.ID,
						Sex = propertyTemplateInfo.Sex,
						ActiveCount = CountInGroup(propertyTemplateInfo.ID),
						ActiveDress = ""
					};
					AddAvatarColectionInfo(propertyTemplateInfo.ID, propertyTemplateInfo.Sex, info);
				}
			}
		}

		public void AddAvatarColectionInfo(int dataId, int sex, UserAvatarColectionInfo info)
		{
			if (AvatarCollectionMgr.FindClothPropertyTemplateInfo(dataId) == null)
			{
				return;
			}
			lock (m_lock)
			{
				if (!m_avatarColect.ContainsKey(dataId))
				{
					m_avatarColect.Add(dataId, info);
				}
			}
		}

		public void AvatarColectionInfoChange()
		{
			Player.Out.SendAvatarColectionAllInfo(Player.PlayerId, AvatarColect);
		}

		public BaseAttributes GetAttributes()
		{
			BaseAttributes Attr = new BaseAttributes();
			foreach (ClothPropertyTemplateInfo propertyTemplateInfo in AvatarCollectionMgr.GetClothPropertyTemplateInfos())
			{
				if (AvatarColect.ContainsKey(propertyTemplateInfo.ID) && AvatarColect[propertyTemplateInfo.ID].IsValidate && AvatarColect[propertyTemplateInfo.ID].HaftActive())
				{
					int multiplier = ((!AvatarColect[propertyTemplateInfo.ID].FullActive()) ? 1 : 2);
					Attr.Attack += propertyTemplateInfo.Attack * multiplier;
					Attr.Defence += propertyTemplateInfo.Defend * multiplier;
					Attr.Agility += propertyTemplateInfo.Agility * multiplier;
					Attr.Luck += propertyTemplateInfo.Luck * multiplier;
					Attr.Damage += propertyTemplateInfo.Damage * multiplier;
					Attr.Armor += propertyTemplateInfo.Guard * multiplier;
					Attr.Blood += propertyTemplateInfo.Blood * multiplier;
				}
			}
			return Attr;
		}

		public UserAvatarColectionInfo GetAvatar(int dataId)
		{
			return AvatarColect.ContainsKey(dataId) ? AvatarColect[dataId] : null;
		}

		public bool ActiveAvatarColection(int dataId, List<int> ids, int sex, int type)
		{
			lock (m_lock)
			{
				using (List<int>.Enumerator enumerator = ids.GetEnumerator())
				{
					if (enumerator.MoveNext())
					{
						int current = enumerator.Current;
						ItemInfo itemByTemplateId = Player.EquipBag.GetItemByTemplateID(0, current);
						if (itemByTemplateId != null && m_avatarColect.ContainsKey(dataId))
						{
							string activeDress = m_avatarColect[dataId].ActiveDress;
							if (activeDress.IndexOf(current.ToString(), StringComparison.Ordinal) != -1)
							{
								return false;
							}
							if (string.IsNullOrEmpty(activeDress))
							{
								List<ClothGroupTemplateInfo> groupTemplateInfo = AvatarCollectionMgr.FindClothGroupTemplateInfo(dataId);
								m_avatarColect[dataId].ActiveDress = FindDressActiveInBag(groupTemplateInfo);
								if (m_avatarColect[dataId].ActiveCount < groupTemplateInfo.Count)
								{
									m_avatarColect[dataId].ActiveCount = groupTemplateInfo.Count;
								}
								m_avatarColect[dataId].ActiveDress = current.ToString();
							}
							else
							{
								m_avatarColect[dataId].ActiveDress = activeDress + "," + current;
							}
							itemByTemplateId.IsBinds = true;
							Player.EquipBag.UpdateItem(itemByTemplateId);
						}
						return true;
					}
				}
			}
			return false;
		}

		public bool DelayAvatarColection(int dataId, int delay, int type)
		{
			if (!AvatarColect.ContainsKey(dataId))
			{
				return false;
			}
			lock (m_lock)
			{
				if (!AvatarColect[dataId].IsValidate)
				{
					m_avatarColect[dataId].EndTime = DateTime.Now;
				}
				m_avatarColect[dataId].EndTime = AvatarColect[dataId].EndTime.AddDays(delay);
				if (m_avatarColect[dataId].FullActive() || m_avatarColect[dataId].HaftActive())
				{
					Player.Out.SendAvatarUpdateProperty(Player);
				}
				return true;
			}
		}

		public string FindDressActiveInBag(List<ClothGroupTemplateInfo> groups)
		{
			List<int> source = new List<int>();
			foreach (ClothGroupTemplateInfo group in groups)
			{
				ItemInfo itemByTemplateId = m_player.EquipBag.GetItemByTemplateID(0, group.TemplateID);
				if (itemByTemplateId != null && !source.Contains(itemByTemplateId.TemplateID))
				{
					source.Add(itemByTemplateId.TemplateID);
				}
			}
			return (source.Count == 0) ? "" : source.Aggregate("", (string current, int id) => current + "," + id).Substring(1);
		}

		public void UpdateCurrentDressModels()
		{
			m_player.Out.SendSetDressModelArr(m_player.PlayerCharacter.ID, DressModelArr);
			m_player.Out.SendCurentDressModel(m_player.PlayerCharacter.ID, CurrentModelIndex);
		}

		public void SaveDressModel(int slot, List<ItemInfo> dresses)
		{
			if (m_dressModelArr.ContainsKey(slot))
			{
				m_dressModelArr[slot] = dresses;
			}
			else if (m_dressModelArr.Count < 6)
			{
				m_dressModelArr.Add(slot, dresses);
			}
		}

		public void SetCurrentModel(int slot)
		{
			if (m_dressModelArr.ContainsKey(slot))
			{
				m_currentModelIndex = slot;
				m_player.Out.SendCurentDressModel(m_player.PlayerCharacter.ID, slot);
			}
			else
			{
				m_currentModelIndex = 0;
				m_player.Out.SendCurentDressModel(m_player.PlayerCharacter.ID, slot);
			}
		}

		public string DressModelArrToString()
		{
			string str1 = "";
			foreach (int key in m_dressModelArr.Keys)
			{
				if (m_dressModelArr[key].Count > 0)
				{
					string str2 = m_dressModelArr[key].Aggregate("", (string current, ItemInfo info) => current + $"{info.ItemID}|{info.TemplateID};");
					string str3 = str2.Substring(0, str2.Length - 1);
					str1 = str1 + str3 + ",";
				}
				else
				{
					str1 += ",";
				}
			}
			return str1;
		}

		public void StringToDressModelArr(string modelArr)
		{
			int slot = 0;
			char[] chArray = new char[1] { ',' };
			string[] array = modelArr.Split(chArray);
			foreach (string str2 in array)
			{
				if (!string.IsNullOrWhiteSpace(str2))
				{
					List<ItemInfo> list = (from item in str2.Split(';').Select(FindEquipDressModel)
						where item != null
						select item).ToList();
					SaveDressModel(slot, list);
				}
				else
				{
					SaveDressModel(slot, new List<ItemInfo>());
				}
				slot++;
			}
			if (CurrentModelIndex > slot)
			{
				CurrentModelIndex = slot;
			}
		}

		public ItemInfo FindEquipDressModel(string id)
		{
			return m_player.EquipBag.GetEquipDressModel(int.Parse(id.Split('|')[0]));
		}
	}
}
