namespace SqlDataProvider.Data
{
	public class UserDressModelInfo : DataObject
	{
		private int _ID;

		private int _UserID;

		private int _SlotID;

		private int _ItemID;

		private int _TemplateID;

		private int _CategoryID;

		private bool _IsDelete;

		public int ID
		{
			get
			{
				return _ID;
			}
			set
			{
				if (_ID != value)
				{
					_ID = value;
					_isDirty = true;
				}
			}
		}

		public int UserID
		{
			get
			{
				return _UserID;
			}
			set
			{
				if (_UserID != value)
				{
					_UserID = value;
					_isDirty = true;
				}
			}
		}

		public int SlotID
		{
			get
			{
				return _SlotID;
			}
			set
			{
				if (_SlotID != value)
				{
					_SlotID = value;
					_isDirty = true;
				}
			}
		}

		public int ItemID
		{
			get
			{
				return _ItemID;
			}
			set
			{
				if (_ItemID != value)
				{
					_ItemID = value;
					_isDirty = true;
				}
			}
		}

		public int TemplateID
		{
			get
			{
				return _TemplateID;
			}
			set
			{
				if (_TemplateID != value)
				{
					_TemplateID = value;
					_isDirty = true;
				}
			}
		}

		public int CategoryID
		{
			get
			{
				return _CategoryID;
			}
			set
			{
				if (_CategoryID != value)
				{
					_CategoryID = value;
					_isDirty = true;
				}
			}
		}

		public bool IsDelete
		{
			get
			{
				return _IsDelete;
			}
			set
			{
				if (_IsDelete != value)
				{
					_IsDelete = value;
					_isDirty = true;
				}
			}
		}
	}
}
