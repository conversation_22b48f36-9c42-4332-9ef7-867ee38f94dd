using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading;
using Bussiness;
using Bussiness.Managers;
using Game.Base.Packets;
using Game.Logic;
using Game.Logic.Phy.Object;
using Game.Server.Achievements;
using Game.Server.Buffer;
using Game.Server.EventSystem;
using Game.Server.EventSystem.Events;
using Game.Server.Farm;
using Game.Server.Games.LittleGame;
using Game.Server.Games.LittleGame.Data;
using Game.Server.Games.LittleGame.Objects;
using Game.Server.GameUtils;
using Game.Server.GypsyShop;
using Game.Server.HotSpringRooms;
using Game.Server.Managers;
using Game.Server.Packets.Client;
using Game.Server.Quests;
using Game.Server.Rooms;
using Game.Server.SceneMarryRooms;
using Game.Server.Statics;
using Game.Server.WarPass;
using log4net;
using Packets.Type;
using SqlDataProvider.Data;
using SqlDataProvider.Data.GmActivity;

namespace Game.Server.GameObjects
{
	public class GamePlayer : IGamePlayer
	{
		public delegate void PlayerEventHandle(GamePlayer player);

		public delegate void PlayerItemPropertyEventHandle(int templateID, int Count);

		public delegate void PlayerGameOverEventHandle(AbstractGame game, bool isWin, int gainXp);

		public delegate void PlayerGameOverTeamCountEventHandle(AbstractGame game, bool isWin, int gainXp, int TeamCount);

		public delegate void PlayerMissionOverEventHandle(AbstractGame game, int missionId, bool isWin);

		public delegate void PlayerMissionTurnOverEventHandle(AbstractGame game, int missionId, int turnNum);

		public delegate void PlayerItemStrengthenEventHandle(int categoryID, int level);

		public delegate void PlayerShopEventHandle(int money, int gold, int offer, int gifttoken, int medal, string payGoods);

		public delegate void PlayerAdoptPetEventHandle();

		public delegate void PlayerNewGearEventHandle(int CategoryID);

		public delegate void PlayerUpLevelPetEventHandle(int templateId, int grade);

		public delegate void PlayerCropPrimaryEventHandle();

		public delegate void PlayerSeedFoodPetEventHandle();

		public delegate void PlayerUserToemGemstoneEventHandle();

		public delegate void PlayerUnknowQuestConditionEventHandle();

		public delegate void PlayerItemInsertEventHandle();

		public delegate void PlayerItemFusionEventHandle(int fusionType);

		public delegate void PlayerItemMeltEventHandle(int categoryID);

		public delegate void PlayerGameKillEventHandel(AbstractGame game, int type, int id, bool isLiving, int demage);

		public delegate void PlayerOwnConsortiaEventHandle();

		public delegate void PlayerItemComposeEventHandle(int composeType);

		public delegate void GameKillDropEventHandel(AbstractGame game, int type, int npcId, bool playResult);

		public delegate void PlayerOnlineEventHandel();

		public delegate void PlayerMoneyChargeHandle(int money);

		public delegate void PlayerAcademyEventHandle(GamePlayer friendly, int type);

		public delegate void PlayerItemStrengthenNewEventHandle(int templateId, int categoryId, int level);

		public delegate void PlayerValidConsumeMoney(int money);

		public delegate void PlayerUpTotemLevelEventHandle(int grade);

		public delegate void FigSpiritLevelEvent(int grade);

		public delegate void PlayerPropertisChange(PlayerInfo player);

		public delegate void CardUpdateEventHandle(int templateId, int level);

		public delegate void NewDayEvent(GamePlayer player);

		public delegate void MasterApprenticeshipEventHandle();

		public delegate void PlayerVipLevelUpEventHandle(int level);

		public delegate void PlayerMarryTeamEventHandle(AbstractGame game, bool isWin, int gainXp, int countPlayersTeam);

        //NOVO SPA BY MOISES
        public delegate void PlayerOwnSpaEventHandle(int onlineTimeSpa);
        public delegate void PlayerEnterHotSpring(GamePlayer player);
        public delegate void EnterSpaEventHandle();
        public delegate void PlayerHotSpingExpAdd(int minutes, int exp);

        private HotSpringRoom _hotSpringRoom0;

        public int HotDirection;
        public int HotX;
        public int HotY;

        private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		private ePlayerState m_playerState;

		public DateTime BoxBeginTime;

		public DateTime StrengthenTime;

		protected GypsyShopLogicProcessor m_gypsyShopProcessor;

		private GypsyShopProcessor gypsyShopProcessor_0;

		protected BaseGame m_game;

		private int m_playerId;

		protected GameClient m_client;

		protected Player m_players;

		private PlayerInfo m_character;

		private string m_account;

		private int m_immunity = 255;

		public bool m_toemview;

		public int FightPower;

		private bool m_isMinor;

		private bool m_isAASInfo;

		private long m_pingTime;

		private char[] m_pvepermissions;

		public long PingStart;

		private bool m_showPP;

		private List<BufferInfo> m_fightBuffInfo;

		private UsersPetinfo m_pet;

		private PlayerEquipInventory m_equipBag;

		private PlayerMagicStoneInventory m_magicStoneBag;

		private PlayerInventory m_propBag;

		private PlayerInventory m_fightBag;

		private PlayerInventory m_ConsortiaBag;

		private PlayerInventory m_storeBag;

		private PlayerInventory m_tempBag;

		private PlayerInventory m_caddyBag;

		private PlayerInventory m_farmBag;

		private PlayerInventory m_vegetable;

		private PlayerInventory m_food;

		private PlayerInventory m_petEgg;

		private PlayerBeadInventory m_BeadBag;

		private CardInventory m_cardBag;

		private PlayerFarm m_farm;

		private PetInventory m_petBag;

		private PlayerTreasure m_treasure;

		private PlayerProperty m_playerProp;

		private PlayerRank m_rank;

		private PlayerDressModel m_dressmodel;

		private PlayerDice m_dice;

		private PlayerBattle m_battle;

		private PlayerExtra m_extra;

		private PlayerActives m_actives;

		private AchievementInventory m_achievementInventory;

		private QuestInventory m_questInventory;

		private BufferList m_bufferList;

		private UserLabyrinthInfo m_Labyrinth;

		private long m_CheckSpeed;

		private List<UserGemStone> m_GemStone;

		public List<ItemInfo> LotteryAwardList;

		private Dictionary<int, UserDrillInfo> m_userDrills;

		private List<ItemInfo> m_equipEffect;

		private int m_changed;

		private static readonly int[] StyleIndex = new int[15]
		{
			1, 2, 3, 4, 5, 6, 11, 13, 14, 15,
			16, 17, 18, 19, 20
		};

		public double GPAddPlus;

		public double OfferAddPlus = 1.0;

		public double GuildRichAddPlus = 1.0;

		public DateTime LastChatTime;

		public DateTime LastFigUpTime;

		public DateTime LastDrillUpTime;

		public DateTime LastOpenPack;

		public DateTime LastOpenGrowthPackage;

		public DateTime LastOpenChristmasPackage;

		public DateTime LastOpenYearMonterPackage;

		public DateTime LastOpenCard;

		public DateTime LastAttachMail;

		public DateTime LastEnterWorldBoss;

		public bool KickProtect;

		private ItemInfo m_MainWeapon;

		private ItemInfo m_healstone;

		private ItemInfo m_currentSecondWeapon;

		public readonly string[] labyrinthGolds = new string[40]
		{
			"0|0", "2|2", "0|0", "2|2", "0|0", "2|3", "0|0", "3|3", "0|0", "3|4",
			"0|0", "3|4", "0|0", "4|5", "0|0", "4|5", "0|0", "4|6", "0|0", "5|6",
			"0|0", "5|7", "0|0", "5|7", "0|0", "6|8", "0|0", "6|8", "0|0", "6|10",
			"0|0", "8|10", "0|0", "8|11", "0|0", "8|11", "0|0", "10|12", "0|0", "10|12"
		};

		private List<int> _viFarms;

		private Dictionary<int, int> _friends;

		private BaseRoom m_currentRoom;

		public int CurrentRoomIndex;

		public int CurrentRoomTeam;

		public int WorldBossMap;

		public bool IsInWorldBossRoom;

		public bool IsInChristmasRoom;

		public byte States;

		public bool isPowerFullUsed;

		public int winningStreak;

		public Dictionary<int, CardInfo> Card = new Dictionary<int, CardInfo>();

		public CardInfo[] CardsTakeOut = new CardInfo[9];

		public int canTakeOut;

		public int takeoutCount;

		public int X;

		public int Y;

		public int MarryMap;

		private BaseSevenDoubleRoom m_currentSevenDoubleRoom;

		private MarryRoom m_currentMarryRoom;

		public int Hot_X;

		public int Hot_Y;

		public int HotMap;

		private UTF8Encoding m_converter;

		private static char[] permissionChars = new char[4] { '1', '3', '7', 'F' };

		private Dictionary<string, object> m_tempProperties = new Dictionary<string, object>();

		private AvatarCollectionInventory m_avatarCollectionBag;

		private bool m_isViewer;

		private GmActivityRankVo m_GmActivityRank;

		protected FarmLogicProcessor m_farmProcessor;

		private FarmProcessor farmProcessor_0;

		private bool m_isView;

		protected WarPassLogicProcessor m_warPassProcessor = new WarPassLogicProcessor();

		private WarPassProcessor m_warPassHandler;

		private PlayerForcesWarPass m_forcesWarPass;

		public PlayerLittleGameInfo LittleGameInfo { get; }

		public int ZoneId => GameServer.Instance.Configuration.ServerID;

		public string ZoneName => GameServer.Instance.Configuration.ServerName;

		public AvatarCollectionInventory AvatarCollectionBag => m_avatarCollectionBag;

		public GmActivityInventory GmActivityInventory { get; }

		public bool IsViewer
		{
			get
			{
				return m_isViewer;
			}
			set
			{
				m_isViewer = value;
			}
		}

		public ePlayerState PlayerState
		{
			get
			{
				return m_playerState;
			}
			set
			{
				m_playerState = value;
			}
		}

		public BaseGame game
		{
			get
			{
				return m_game;
			}
			set
			{
				m_game = value;
			}
		}

		public int Immunity
		{
			get
			{
				return m_immunity;
			}
			set
			{
				m_immunity = value;
			}
		}

		public int PlayerId => m_playerId;

		public bool Toemview
		{
			get
			{
				return m_toemview;
			}
			set
			{
				m_toemview = value;
			}
		}

		public string Account => m_account;

		public PlayerInfo PlayerCharacter => m_character;

		public UserMatchInfo MatchInfo => m_battle.MatchInfo;

		public GameClient Client => m_client;

		public Player Players => m_players;

		public bool IsActive => m_client.IsConnected;

		public IPacketLib Out => m_client.Out;

		public bool IsMinor
		{
			get
			{
				return m_isMinor;
			}
			set
			{
				m_isMinor = value;
			}
		}

		public bool IsAASInfo
		{
			get
			{
				return m_isAASInfo;
			}
			set
			{
				m_isAASInfo = value;
			}
		}

		public bool BalancedMode { get; set; }

		public long PingTime
		{
			get
			{
				return m_pingTime;
			}
			set
			{
				m_pingTime = value;
				GSPacketIn pkg = Out.SendNetWork(this, m_pingTime);
				if (m_currentRoom != null)
				{
					m_currentRoom.SendToAll(pkg, this);
				}
			}
		}

		public bool ShowPP
		{
			get
			{
				return m_showPP;
			}
			set
			{
				m_showPP = value;
			}
		}

		public List<BufferInfo> FightBuffs
		{
			get
			{
				return m_fightBuffInfo;
			}
			set
			{
				m_fightBuffInfo = value;
			}
		}

		public UsersPetinfo Pet => m_pet;

		public PlayerExtra Extra => m_extra;

		public PlayerBattle BattleData => m_battle;

		public PlayerActives Actives => m_actives;

		public HomeTempPracticeData Temple { get; private set; }

		public PlayerDice Dice => m_dice;

		public PlayerProperty PlayerProp => m_playerProp;

		public PlayerRank Rank => m_rank;

		public PlayerDressModel DressModel => m_dressmodel;

		public PlayerTreasure Treasure => m_treasure;

		public PetInventory PetBag => m_petBag;

		public PlayerFarm Farm => m_farm;

		public PlayerEquipInventory EquipBag => m_equipBag;

		public PlayerInventory PropBag => m_propBag;

		public PlayerInventory FightBag => m_fightBag;

		public PlayerInventory TempBag => m_tempBag;

		public PlayerInventory ConsortiaBag => m_ConsortiaBag;

		public PlayerInventory StoreBag => m_storeBag;

		public PlayerInventory CaddyBag => m_caddyBag;

		public PlayerInventory FarmBag => m_farmBag;

		public PlayerInventory Vegetable => m_vegetable;

		public PlayerInventory Food => m_food;

		public PlayerInventory PetEgg => m_petEgg;

		public PlayerBeadInventory BeadBag => m_BeadBag;

		public PlayerMagicStoneInventory MagicStoneBag => m_magicStoneBag;

		public CardInventory CardBag => m_cardBag;

		public AchievementInventory AchievementInventory => m_achievementInventory;

		public QuestInventory QuestInventory => m_questInventory;

		public BufferList BufferList => m_bufferList;

		public UserLabyrinthInfo Labyrinth
		{
			get
			{
				return m_Labyrinth;
			}
			set
			{
				m_Labyrinth = value;
			}
		}

		public GmActivityRankVo GmActivityRank
		{
			get
			{
				return m_GmActivityRank;
			}
			set
			{
				m_GmActivityRank = value;
			}
		}

		public List<UserGemStone> GemStone
		{
			get
			{
				return m_GemStone;
			}
			set
			{
				m_GemStone = value;
			}
		}

		public Dictionary<int, UserDrillInfo> UserDrills
		{
			get
			{
				return m_userDrills;
			}
			set
			{
				m_userDrills = value;
			}
		}

		public List<ItemInfo> EquipEffect
		{
			get
			{
				return m_equipEffect;
			}
			set
			{
				m_equipEffect = value;
			}
		}

		public bool CanUseProp { get; set; }

		public bool CanX2Exp { get; set; }

		public bool CanX3Exp { get; set; }

		public int Level
		{
			get
			{
				return m_character.Grade;
			}
			set
			{
				if (value != m_character.Grade)
				{
					m_character.Grade = value;
					int grade = m_character.Grade;
					OnLevelUp(value);
					if (Extra.CheckNoviceActiveOpen(NoviceActiveType.GRADE_UP_ACTIVE))
					{
						Extra.UpdateEventCondition(1, m_character.Grade);
					}
					if (value == 8)
					{
						PlayerCharacter.WeaklessGuildProgressStr = "////b7D/ht8WDQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=";
					}
					if (m_character.masterID != 0 && grade < m_character.Grade)
					{
						AcademyMgr.UpdateAwardApp(this, grade);
					}
					OnPropertiesChanged();
				}
			}
		}

		public int RankAverageLv { get; set; }

		public int LevelPlusBlood => LevelMgr.FindLevel(m_character.Grade).Blood;

		public ItemTemplateInfo MainWeapon
		{
			get
			{
				if (m_MainWeapon == null)
				{
					return null;
				}
				if (m_MainWeapon.IsValidGoldItem() && m_MainWeapon.GoldEquip != null)
				{
					return m_MainWeapon.GoldEquip;
				}
				return m_MainWeapon.Template;
			}
		}

		public ItemInfo Healstone
		{
			get
			{
				if (m_healstone == null)
				{
					return null;
				}
				return m_healstone;
			}
		}

		public ItemInfo SecondWeapon
		{
			get
			{
				if (m_currentSecondWeapon == null)
				{
					return null;
				}
				return m_currentSecondWeapon;
			}
		}

		public string ProcessLabyrinthAward { get; set; }

		public List<int> ViFarms => _viFarms;

		public Dictionary<int, int> Friends => _friends;

		public BaseRoom CurrentRoom
		{
			get
			{
				return m_currentRoom;
			}
			set
			{
				BaseRoom baseRoom = Interlocked.Exchange(ref m_currentRoom, value);
				if (baseRoom != null)
				{
					RoomMgr.ExitRoom(baseRoom, this);
				}
			}
		}

		public int GamePlayerId { get; set; }

		public long WorldbossBood { get; set; }

		public long AllWorldDameBoss { get; set; }

		public BaseSevenDoubleRoom CurrentSevenDoubleRoom
		{
			get
			{
				return m_currentSevenDoubleRoom;
			}
			set
			{
				m_currentSevenDoubleRoom = value;
			}
		}

		public MarryRoom CurrentMarryRoom
		{
			get
			{
				return m_currentMarryRoom;
			}
			set
			{
				m_currentMarryRoom = value;
			}
		}

		public bool IsInMarryRoom => m_currentMarryRoom != null;

		public int ServerID { get; set; }

		public Dictionary<string, object> TempProperties => m_tempProperties;

		public bool UseDungeonTicket { get; set; }

		public FarmProcessor FarmHandler => farmProcessor_0;

		public bool CommandsKill { get; set; }

		public int Lottery { get; internal set; }

		public List<ItemBoxInfo> LotteryItems { get; internal set; }

		public int LotteryID { get; internal set; }

		public EveryDayActiveDetailInfo EveryDayActiveDetail { get; set; }

		public GypsyShopProcessor GypsyShopHandler => gypsyShopProcessor_0;

		public bool IsView
		{
			get
			{
				return m_isView;
			}
			set
			{
				m_isView = value;
			}
		}

		public long CheckSpeed
		{
			get
			{
				return m_CheckSpeed;
			}
			set
			{
				m_CheckSpeed = value;
			}
		}

		public WarPassProcessor WarPassHandler => m_warPassHandler;

		public PlayerForcesWarPass ForcesWarPass => m_forcesWarPass;

		public event PlayerEventHandle PingTimeOnline;

		public event PlayerEventHandle UseBuffer;

		public event PlayerEventHandle LevelUp;

		public event PlayerItemPropertyEventHandle AfterUsingItem;

		public event PlayerGameOverEventHandle GameOver;

		public event PlayerGameOverTeamCountEventHandle GameOverTeamCount;

		public event PlayerAcademyEventHandle AcademyEvent;

		public event MasterApprenticeshipEventHandle MasterApprenticeship;

		public event PlayerGameOverEventHandle OnMarryMatchIsOver;

		public event PlayerMissionOverEventHandle MissionOver;

		public event PlayerMissionTurnOverEventHandle MissionTurnOver;

		public event PlayerItemStrengthenEventHandle ItemStrengthen;

		public event PlayerShopEventHandle Paid;

		public event PlayerAdoptPetEventHandle AdoptPetEvent;

		public event PlayerNewGearEventHandle NewGearEvent;

		public event PlayerUpLevelPetEventHandle UpLevelPetEvent;

		public event PlayerCropPrimaryEventHandle CropPrimaryEvent;

		public event PlayerSeedFoodPetEventHandle SeedFoodPetEvent;

		public event PlayerUserToemGemstoneEventHandle UserToemGemstonetEvent;

		public event PlayerUnknowQuestConditionEventHandle UnknowQuestConditionEvent;

		public event PlayerItemInsertEventHandle ItemInsert;

		public event PlayerItemFusionEventHandle ItemFusion;

		public event PlayerItemMeltEventHandle ItemMelt;

		public event PlayerGameKillEventHandel AfterKillingLiving;

		public event PlayerOwnConsortiaEventHandle GuildChanged;

		public event PlayerItemComposeEventHandle ItemCompose;

		public event GameKillDropEventHandel GameKillDrop;

		public event PlayerOnlineEventHandel TimeOnline;

		public event PlayerMoneyChargeHandle MoneyCharge;

		public event CardUpdateEventHandle CardUpdateEvent;

		public event NewDayEvent NewDayReachedEvent;

		public event PlayerPropertisChange PropertiesChange;

		public event FigSpiritLevelEvent UpFigSpiritLevelEvent;

		public event PlayerUpTotemLevelEventHandle UpTotemLevelEvent;

		public event PlayerValidConsumeMoney MoneyConsume;

		public event PlayerItemStrengthenNewEventHandle ItemStrengthenNew;

		public event PlayerVipLevelUpEventHandle PlayerVipLevelUpEvent;

		public event PlayerMarryTeamEventHandle GameMarryTeam;

        //NEW SPA BY MOISES S2 - Events
        public event PlayerHotSpingExpAdd HotSpingExpAdd;
        public event PlayerEnterHotSpring EnterHotSpringEvent;
        public event EnterSpaEventHandle EnterSpaEvent;
        public event PlayerOwnSpaEventHandle PlayerSpa;

        public float GetWorldExpProb()
		{
			float num = 0f;
			return (float)(1.0 + (1.0 - (110.0 - (double)RankAverageLv) / (110.0 - (double)Level)) * 2.0);
		}

		public GamePlayer(int playerId, string account, GameClient client, PlayerInfo info)
		{
			m_playerId = playerId;
			m_account = account;
			m_client = client;
			m_character = info;
			m_equipBag = new PlayerEquipInventory(this);
			m_BeadBag = new PlayerBeadInventory(this);
			m_magicStoneBag = new PlayerMagicStoneInventory(this);
			m_propBag = new PlayerInventory(this, saveTodb: true, 96, 1, 0, autoStack: true);
			m_ConsortiaBag = new PlayerInventory(this, saveTodb: true, 100, 11, 0, autoStack: true);
			m_storeBag = new PlayerInventory(this, saveTodb: true, 20, 12, 0, autoStack: true);
			m_fightBag = new PlayerInventory(this, saveTodb: false, 3, 3, 0, autoStack: false);
			m_tempBag = new PlayerInventory(this, saveTodb: false, 100, 4, 0, autoStack: true);
			m_caddyBag = new PlayerInventory(this, saveTodb: false, 30, 5, 0, autoStack: true);
			m_farmBag = new PlayerInventory(this, saveTodb: true, 30, 13, 0, autoStack: true);
			m_vegetable = new PlayerInventory(this, saveTodb: true, 30, 14, 0, autoStack: true);
			m_food = new PlayerInventory(this, saveTodb: true, 30, 34, 0, autoStack: true);
			m_petEgg = new PlayerInventory(this, saveTodb: true, 30, 35, 0, autoStack: true);
			m_cardBag = new CardInventory(this, saveTodb: true, 100, 0);
			m_farm = new PlayerFarm(this, saveTodb: true, 30, 0);
			m_petBag = new PetInventory(this, saveTodb: true, 10, 8, 0);
			m_treasure = new PlayerTreasure(this, saveTodb: true);
			m_rank = new PlayerRank(this, saveTodb: true);
			m_dressmodel = new PlayerDressModel(this, saveTodb: true);
			m_avatarCollectionBag = new AvatarCollectionInventory(this);
			m_playerProp = new PlayerProperty(this);
			m_dice = new PlayerDice(this, saveTodb: true);
			m_battle = new PlayerBattle(this, saveTodb: true);
			m_actives = new PlayerActives(this, saveTodb: true);
			m_extra = new PlayerExtra(this, saveTodb: true);
			m_questInventory = new QuestInventory(this);
			m_achievementInventory = new AchievementInventory(this);
			m_bufferList = new BufferList(this);
			m_fightBuffInfo = new List<BufferInfo>();
			m_equipEffect = new List<ItemInfo>();
			m_GemStone = new List<UserGemStone>();
			m_gypsyShopProcessor = new GypsyShopLogicProcessor();
			m_userDrills = new Dictionary<int, UserDrillInfo>();
			m_Labyrinth = null;
			RankAverageLv = 0;
			GPAddPlus = 1.0;
			m_toemview = true;
			X = 646;
			Y = 1241;
			MarryMap = 0;
			m_isView = false;
			LastChatTime = DateTime.Today;
			LastFigUpTime = DateTime.Today;
			LastDrillUpTime = DateTime.Today;
			LastOpenPack = DateTime.Today;
			LastOpenGrowthPackage = DateTime.Now;
			LastOpenChristmasPackage = DateTime.Now;
			LastOpenYearMonterPackage = DateTime.Now;
			m_showPP = false;
			m_converter = new UTF8Encoding();
			Temple = new HomeTempPracticeData();
			GmActivityInventory = new GmActivityInventory(this);
			m_farmProcessor = new FarmLogicProcessor();
			LittleGameInfo = new PlayerLittleGameInfo
			{
				Actions = new TriggeredQueue<string, GamePlayer>(this),
				X = 275,
				Y = 30
			};
			m_CheckSpeed = (long)DateTime.UtcNow.Subtract(new DateTime(1970, 1, 1)).TotalSeconds;
			UseDungeonTicket = false;
			BalancedMode = false;
			ResetLottery();
			m_forcesWarPass = new PlayerForcesWarPass(this, saveTodb: true);
		}

		public void ResetLottery()
		{
			Lottery = -1;
			LotteryID = 0;
			LotteryItems = new List<ItemBoxInfo>();
			LotteryAwardList = new List<ItemInfo>();
		}

		public PlayerInventory GetInventory(eBageType bageType)
		{
			if (bageType <= eBageType.BeadBag)
			{
				switch (bageType)
				{
				case eBageType.EquipBag:
					return m_equipBag;
				case eBageType.PropBag:
					return m_propBag;
				case eBageType.FightBag:
					return m_fightBag;
				case eBageType.TempBag:
					return m_tempBag;
				case eBageType.CaddyBag:
					return m_caddyBag;
				case eBageType.Consortia:
					return m_ConsortiaBag;
				case eBageType.Store:
					return m_storeBag;
				case eBageType.FarmBag:
					return m_farmBag;
				case eBageType.Vegetable:
					return m_vegetable;
				case eBageType.BeadBag:
					return m_BeadBag;
				}
			}
			else
			{
				switch (bageType)
				{
				case eBageType.Food:
					return m_food;
				case eBageType.PetEgg:
					return m_petEgg;
				}
				if (bageType == eBageType.MagicStone)
				{
					return m_magicStoneBag;
				}
			}
			throw new NotSupportedException($"Did not support this type bag: {bageType} PlayerID: {PlayerCharacter.ID} Nickname: {PlayerCharacter.NickName}");
		}

		public PlayerInventory GetItemInventory(ItemTemplateInfo template)
		{
			return GetInventory(template.BagType);
		}

		public ItemInfo GetItemAt(eBageType bagType, int place)
		{
			return GetInventory(bagType)?.GetItemAt(place);
		}

		public ItemInfo GetItemByTemplateID(int templateID)
		{
			PlayerInventory inventory = GetInventory(eBageType.EquipBag);
			ItemInfo itemByTemplateID = inventory.GetItemByTemplateID(31, templateID);
			if (itemByTemplateID == null)
			{
				inventory = GetInventory(eBageType.PropBag);
				itemByTemplateID = inventory.GetItemByTemplateID(0, templateID);
			}
			if (itemByTemplateID == null)
			{
				inventory = GetInventory(eBageType.Consortia);
				itemByTemplateID = inventory.GetItemByTemplateID(0, templateID);
			}
			return itemByTemplateID;
		}

		public ItemInfo GetItemByItemID(int ItemID)
		{
			PlayerInventory inventory = GetInventory(eBageType.EquipBag);
			ItemInfo itemByItemID = inventory.GetItemByItemID(31, ItemID);
			if (itemByItemID == null)
			{
				inventory = GetInventory(eBageType.PropBag);
				itemByItemID = inventory.GetItemByItemID(0, ItemID);
			}
			if (itemByItemID == null)
			{
				inventory = GetInventory(eBageType.Consortia);
				itemByItemID = inventory.GetItemByItemID(0, ItemID);
			}
			return itemByItemID;
		}

		public int GetItemCount(int templateId)
		{
			return m_propBag.GetItemCount(templateId) + m_equipBag.GetItemCount(templateId) + m_ConsortiaBag.GetItemCount(templateId);
		}

		public bool AddItem(ItemInfo item)
		{
			AbstractInventory itemInventory = GetItemInventory(item.Template);
			return itemInventory.AddItem(item, itemInventory.BeginSlot);
		}

		public bool StackItemToAnother(ItemInfo item)
		{
			AbstractInventory itemInventory = GetItemInventory(item.Template);
			return itemInventory.StackItemToAnother(item);
		}

		public bool StackItem(ref List<ItemInfo> items)
		{
			List<ItemInfo> list = new List<ItemInfo>();
			foreach (ItemInfo item in items)
			{
				if (item.Template.MaxCount <= 0)
				{
					log.Error("StackItem Error：" + item.Template.TemplateID + "," + item.Template.MaxCount + ",player:" + PlayerCharacter.NickName + "," + PlayerCharacter.ID);
				}
				else
				{
					while (item.Count > item.Template.MaxCount)
					{
						ItemInfo itemInfo = item.Clone();
						itemInfo.BagType = item.BagType;
						itemInfo.Count = item.Template.MaxCount;
						item.Count -= item.Template.MaxCount;
						list.Add(itemInfo);
					}
				}
			}
			foreach (ItemInfo item2 in list)
			{
				items.Add(item2);
			}
			List<ItemInfo> list2 = new List<ItemInfo>();
			for (int i = 0; i < items.Count; i++)
			{
				for (int j = i + 1; j < items.Count; j++)
				{
					if (items[i] != null && items[j] != null && items[i].CanStackedTo(items[j]) && items[j].Count + items[i].Count <= items[i].Template.MaxCount)
					{
						items[j].Count += items[i].Count;
						list2.Add(items[i]);
						break;
					}
				}
			}
			foreach (ItemInfo item3 in list2)
			{
				items.Remove(item3);
			}
			return true;
		}

        //SPA BY MOISES OnEnter
        public void OnPlayerSpa(int onlineTimeSpa) => PlayerSpa?.Invoke(onlineTimeSpa);

        public void OnEnterSpaEvent()
	    => EnterSpaEvent?.Invoke();

        public void OnEnterHotSpring()
	    => EnterHotSpringEvent?.Invoke(this);

        public void OnHotSpingExpAdd(int minutes, int exp) => HotSpingExpAdd?.Invoke(minutes, exp);

        public void UpdateItem(ItemInfo item)
		{
			m_equipBag.UpdateItem(item);
			m_propBag.UpdateItem(item);
		}

		public bool RemoveItem(ItemInfo item)
		{
			if (item.BagType == m_farmBag.BagType)
			{
				return m_farmBag.RemoveItem(item);
			}
			if (item.BagType == m_propBag.BagType)
			{
				return m_propBag.RemoveItem(item);
			}
			if (item.BagType == m_BeadBag.BagType)
			{
				return m_BeadBag.RemoveItem(item);
			}
			if (item.BagType == m_magicStoneBag.BagType)
			{
				return m_magicStoneBag.RemoveItem(item);
			}
			if (item.BagType == m_fightBag.BagType)
			{
				return m_fightBag.RemoveItem(item);
			}
			return m_equipBag.RemoveItem(item);
		}

		public void ClearFightBuffOneMatch()
		{
			List<BufferInfo> list = new List<BufferInfo>();
			foreach (BufferInfo fightBuff in FightBuffs)
			{
				if (fightBuff != null && fightBuff.Type >= 400 && fightBuff.Type <= 406)
				{
					list.Add(fightBuff);
				}
			}
			foreach (BufferInfo item in list)
			{
				FightBuffs.Remove(item);
			}
			list.Clear();
		}

		public void UpdateFightBuff(BufferInfo info)
		{
			int num = -1;
			for (int i = 0; i < FightBuffs.Count; i++)
			{
				if (info != null && info.Type == FightBuffs[i].Type)
				{
					FightBuffs[i] = info;
					num = info.Type;
				}
			}
			if (num == -1)
			{
				FightBuffs.Add(info);
			}
		}

		public void UpdatePveResult(string type, int value, bool isWin)
		{
			int num = 0;
			string text = "";
			if (type != null)
			{
				switch (type)
				{
				case "cryptBoss":
					if (isWin)
					{
						Actives.SendCryptBossAward(value, (CurrentRoom.Game as PVEGame).HandLevel);
					}
					break;
				case "qx":
					if (!isWin)
					{
						List<ItemInfo> info = null;
						DropInventory.CopyAllDrop(value, ref info);
						int num5 = value - 70000;
						if (value >= 70006)
						{
							num5 -= 2;
						}
						string title = "Parabéns por passar ilha dos Piratas Fase " + num5;
						if (info != null)
						{
							WorldEventMgr.SendItemsToMail(info, PlayerCharacter.ID, PlayerCharacter.NickName, title);
						}
					}
					num = 0;
					break;
				case "yearmonter":
					Actives.Info.DamageNum = value;
					Actives.CreateYearMonterBoxState();
					num = 0;
					break;
				case "consortiaboss":
				{
					int num4 = value / 800;
					num = value / 1200;
					text = $"Você Ganhou {num4} pontos de Contribuição e {num} pontos de Honras.";
					AddRichesOffer(num4);
					ConsortiaBossMgr.UpdateBlood(PlayerCharacter.ConsortiaID, value);
					ConsortiaBossMgr.UpdateRank(PlayerCharacter.ConsortiaID, value, num4, num, PlayerCharacter.NickName, PlayerCharacter.ID);
					break;
				}
				case "updateBloodBossWorld":
				{
					int num3 = value / 400;
					num = value / 1200;
					RoomMgr.WorldBossRoom.UpdateRank(num3, num, PlayerCharacter.NickName);
					RoomMgr.WorldBossRoom.ReduceBlood(value);
					AddDamageScores(num3);
					break;
				}
				case "worldboss":
				{
					int num2 = value / 400;
					num = value / 1200;
					text = $"Você Ganhou {num2} pontos de Contribuição e {num} pontos de Honras.";
					if (isWin)
					{
						RoomMgr.WorldBossRoom.FightOverAll();
					}
					break;
				}
				}
			}
			AddHonor(num);
			if (!string.IsNullOrEmpty(text))
			{
				SendMessage(text);
			}
		}

		public void SendItemNotice(ItemInfo info, int typeGet, string Name)
		{
			if (info == null)
			{
				return;
			}
			int num;
			switch (typeGet)
			{
			case 0:
			case 1:
				num = 2;
				break;
			case 2:
			case 3:
			case 4:
				num = 1;
				break;
			default:
				num = 3;
				break;
			}
			GSPacketIn gSPacketIn = new GSPacketIn(14);
			gSPacketIn.WriteString(PlayerCharacter.NickName);
			gSPacketIn.WriteInt(typeGet);
			gSPacketIn.WriteInt(info.TemplateID);
			gSPacketIn.WriteBoolean(info.IsBinds);
			gSPacketIn.WriteInt(num);
			gSPacketIn.WriteInt(info.Count);
			if (num == 3)
			{
				gSPacketIn.WriteString(Name);
			}
			if (info.IsTips || info.Template.Quality >= 5 || info.IsBead())
			{
				GamePlayer[] allPlayers = WorldMgr.GetAllPlayers();
				GamePlayer[] array = allPlayers;
				foreach (GamePlayer gamePlayer in array)
				{
					gamePlayer.Out.SendTCP(gSPacketIn);
				}
			}
		}

		public bool AddTemplate(ItemInfo cloneItem, eBageType bagType, int count, eGameView gameView, string Name)
		{
			PlayerInventory inventory = GetInventory(bagType);
			if (cloneItem != null)
			{
				List<ItemInfo> list = new List<ItemInfo>();
				if (!inventory.StackItemToAnother(cloneItem) && !inventory.AddItem(cloneItem))
				{
					list.Add(cloneItem);
				}
				BagFullSendToMail(list);
				if (Name != "no")
				{
					SendItemNotice(cloneItem, (int)gameView, Name);
				}
				return true;
			}
			return false;
		}

		public void OnPingTimeOnline()
		{
			if (this.PingTimeOnline != null)
			{
				this.PingTimeOnline(this);
			}
		}

		public int NoviceEventOnlineGameadd(int value)
		{
			if (m_character.State == 1)
			{
				Extra.UpdateEventCondition(8, value, isPlus: true);
			}
			return 0;
		}

		public bool AddTemplate(ItemInfo cloneItem, eBageType bagType, int count, eGameView gameView)
		{
			if (eBageType.FightBag == bagType)
			{
				return FightBag.AddItem(cloneItem);
			}
			return AddTemplate(cloneItem, bagType, count, gameView, "no");
		}

		public bool AddTemplate(ItemInfo cloneItem)
		{
			return AddTemplate(cloneItem, cloneItem.Template.BagType, cloneItem.Count, eGameView.OtherTypeGet);
		}

		public bool AddTemplate(ItemInfo cloneItem, string name)
		{
			return AddTemplate(cloneItem, cloneItem.Template.BagType, cloneItem.Count, eGameView.OtherTypeGet, name);
		}

		public bool AddTemplate(List<ItemInfo> infos, int count, eGameView gameView)
		{
			if (infos != null)
			{
				List<ItemInfo> list = new List<ItemInfo>();
				foreach (ItemInfo info in infos)
				{
					info.IsBinds = true;
					info.Count = count;
					if (!StackItemToAnother(info) && !AddItem(info))
					{
						list.Add(info);
					}
				}
				BagFullSendToMail(list);
				return true;
			}
			return false;
		}

		public bool AddTemplate(List<ItemInfo> infos, eGameView typeGet)
		{
			if (infos != null)
			{
				List<ItemInfo> list = new List<ItemInfo>();
				foreach (ItemInfo info in infos)
				{
					info.IsBinds = true;
					if (!StackItemToAnother(info) && !AddItem(info))
					{
						list.Add(info);
					}
				}
				BagFullSendToMail(list);
				return true;
			}
			return false;
		}

		public bool AddTemplate(List<ItemInfo> infos)
		{
			return AddTemplate(infos, eGameView.OtherTypeGet);
		}

		public void BagFullSendToMail(ItemInfo info)
		{
			BagFullSendToMail(new List<ItemInfo> { info });
		}

		public void BagFullSendToMail(List<ItemInfo> infos)
		{
			if (infos.Count > 0 && SendItemsToMail(infos, "", "Sua mochila já está cheia.", eMailType.BuyItem))
			{
				Out.SendMailResponse(PlayerCharacter.ID, eMailRespose.Receiver);
			}
		}

		public bool FindEmptySlot(eBageType bagType)
		{
			PlayerInventory inventory = GetInventory(bagType);
			inventory.FindFirstEmptySlot();
			return inventory.FindFirstEmptySlot() > 0;
		}

		public bool RemoveTemplate(int templateId, int count)
		{
			int itemCount = m_equipBag.GetItemCount(templateId);
			int itemCount2 = m_propBag.GetItemCount(templateId);
			int itemCount3 = m_ConsortiaBag.GetItemCount(templateId);
			int num = itemCount + itemCount2 + itemCount3;
			ItemTemplateInfo itemTemplateInfo = ItemMgr.FindItemTemplate(templateId);
			if (itemTemplateInfo != null && num >= count)
			{
				if (itemCount > 0 && count > 0 && RemoveTempate(eBageType.EquipBag, itemTemplateInfo, (itemCount > count) ? count : itemCount))
				{
					count = ((count >= itemCount) ? (count - itemCount) : 0);
				}
				if (itemCount2 > 0 && count > 0 && RemoveTempate(eBageType.PropBag, itemTemplateInfo, (itemCount2 > count) ? count : itemCount2))
				{
					count = ((count >= itemCount2) ? (count - itemCount2) : 0);
				}
				if (itemCount3 > 0 && count > 0 && RemoveTempate(eBageType.Consortia, itemTemplateInfo, (itemCount3 > count) ? count : itemCount3))
				{
					count = ((count >= itemCount3) ? (count - itemCount3) : 0);
				}
				if (count == 0)
				{
					return true;
				}
				if (log.IsErrorEnabled)
				{
					log.Error($"Item Remover Error：PlayerId {m_playerId} Remover TemplateId{templateId} Is Not Zero!");
				}
			}
			return false;
		}

		public bool RemoveTempate(eBageType bagType, ItemTemplateInfo template, int count)
		{
			return GetInventory(bagType)?.RemoveTemplate(template.TemplateID, count) ?? false;
		}

		public bool RemoveTemplate(ItemTemplateInfo template, int count)
		{
			return GetItemInventory(template)?.RemoveTemplate(template.TemplateID, count) ?? false;
		}

		public bool ClearTempBag()
		{
			TempBag.ClearBag();
			return true;
		}

		public bool ClearFightBag()
		{
			FightBag.ClearBag();
			return true;
		}

		public void ClearCaddyBag()
		{
			List<ItemInfo> list = new List<ItemInfo>();
			for (int i = 0; i < CaddyBag.Capalility; i++)
			{
				ItemInfo itemAt = CaddyBag.GetItemAt(i);
				if (itemAt != null)
				{
					ItemInfo itemInfo = ItemInfo.CloneFromTemplate(itemAt.Template, itemAt);
					itemInfo.Count = 1;
					list.Add(itemInfo);
				}
			}
			CaddyBag.ClearBag();
			AddTemplate(list);
		}

		public void ClearHideBag()
		{
			List<ItemInfo> list = new List<ItemInfo>();
			for (int i = 0; i < StoreBag.Capalility; i++)
			{
				ItemInfo itemAt = StoreBag.GetItemAt(i);
				int num = 0;
				if (itemAt == null)
				{
					continue;
				}
				if (itemAt.Template.BagType == eBageType.PropBag)
				{
					num = PropBag.FindFirstEmptySlot();
					if (!PropBag.StackItemToAnother(itemAt) && !PropBag.AddItemTo(itemAt, num))
					{
						list.Add(itemAt);
					}
					else
					{
						StoreBag.TakeOutItem(itemAt);
					}
				}
				else
				{
					num = EquipBag.FindFirstEmptySlot(31);
					if (!EquipBag.StackItemToAnother(itemAt) && !EquipBag.AddItemTo(itemAt, num))
					{
						list.Add(itemAt);
					}
					else
					{
						StoreBag.TakeOutItem(itemAt);
					}
				}
			}
			if (list.Count > 0)
			{
				SendItemsToMail(list, "Sistema do Ferreiro", "A mochila está cheia, os itens foram devolvidos", eMailType.StoreCanel);
				StoreBag.ClearBag();
			}
		}

		public string ClearHideBag1()
		{
			foreach (ItemInfo itemInfo in StoreBag.GetItems())
			{
				PlayerInventory itemInventory = GetItemInventory(itemInfo.Template);
				string key = $"temp_place_{itemInfo.ItemID}";
				if (TempProperties.ContainsKey(key))
				{
					int tempProperty = (int)TempProperties[key];
					TempProperties.Remove(key);
					if (itemInventory.AddItemTo(itemInfo, tempProperty))
					{
						StoreBag.TakeOutItem(itemInfo);
						StoreBag.UpdateRuneInGoodsDB(itemInfo);
					}
					else if (itemInventory.AddItem(itemInfo))
					{
						StoreBag.TakeOutItem(itemInfo);
						StoreBag.UpdateRuneInGoodsDB(itemInfo);
					}
				}
				else if (itemInventory.StackItemToAnother(itemInfo))
				{
					StoreBag.RemoveItem(itemInfo, eItemRemoveType.Stack);
				}
				else if (itemInventory.AddItem(itemInfo))
				{
					StoreBag.TakeOutItem(itemInfo);
					StoreBag.UpdateRuneInGoodsDB(itemInfo);
				}
			}
			List<ItemInfo> items = StoreBag.GetItems();
			if (items.Count <= 0)
			{
				return string.Empty;
			}
			string translation1 = LanguageMgr.GetTranslation("Game.Server.GameUtils.Content2");
			string translation2 = LanguageMgr.GetTranslation("Game.Server.GameUtils.Title2");
			if (items.Count > 0)
			{
				SendItemsToMail(items, translation1, translation2, eMailType.ItemOverdue);
			}
			Out.SendMailResponse(PlayerCharacter.ID, eMailRespose.Receiver);
			return LanguageMgr.GetTranslation("OpenUpArkHandler.Mail");
		}

		public bool IsConsortia()
		{
			ConsortiaInfo consortiaInfo = ConsortiaMgr.FindConsortiaInfo(PlayerCharacter.ConsortiaID);
			return consortiaInfo != null;
		}

		public void OnUseBuffer()
		{
			if (this.UseBuffer != null)
			{
				this.UseBuffer(this);
			}
		}

		public void AddBeadEffect(ItemInfo item)
		{
			m_equipEffect.Add(item);
		}

		public void BeginAllChanges()
		{
			BeginChanges();
			m_bufferList.BeginChanges();
			m_equipBag.BeginChanges();
			m_propBag.BeginChanges();
			m_BeadBag.BeginChanges();
			m_magicStoneBag.BeginChanges();
			m_farmBag.BeginChanges();
			m_vegetable.BeginChanges();
			m_cardBag.BeginChanges();
		}

		public void CommitAllChanges()
		{
			CommitChanges();
			m_bufferList.CommitChanges();
			m_equipBag.CommitChanges();
			m_propBag.CommitChanges();
			m_BeadBag.CommitChanges();
			m_magicStoneBag.CommitChanges();
			m_farmBag.CommitChanges();
			m_vegetable.CommitChanges();
			m_cardBag.CommitChanges();
		}

		public void BeginChanges()
		{
			Interlocked.Increment(ref m_changed);
		}

		public void CommitChanges()
		{
			Interlocked.Decrement(ref m_changed);
			OnPropertiesChanged();
		}

		protected void OnPropertiesChanged()
		{
			if (m_changed <= 0)
			{
				if (m_changed < 0)
				{
					log.Error("Player changed count < 0");
					Thread.VolatileWrite(ref m_changed, 0);
				}
				UpdateProperties();
			}
		}

		public void UpdateDrill(int index, UserDrillInfo drill)
		{
			m_userDrills[index] = drill;
		}

		public int GetDrillLevel(int place)
		{
			for (int i = 0; i < UserDrills.Count; i++)
			{
				if (UserDrills[i].BeadPlace == place)
				{
					return UserDrills[i].HoleLv;
				}
			}
			return 0;
		}

		public void UpdateBadgeId(int Id)
		{
			m_character.badgeID = Id;
		}

		public void UpdateTimeBox(int receiebox, int receieGrade, int needGetBoxTime)
		{
			m_character.receiebox = receiebox;
			m_character.receieGrade = receieGrade;
			m_character.needGetBoxTime = needGetBoxTime;
		}

		public string GetFightFootballStyle(int team)
		{
			if (team == 1)
			{
				return CreateFightFootballStyle().Split(';')[0];
			}
			return CreateFightFootballStyle().Split(';')[1];
		}

		public string CreateFightFootballStyle()
		{
			ItemInfo itemAt = GetItemAt(eBageType.EquipBag, 0);
			string text = ((itemAt == null) ? "" : (itemAt.TemplateID + "|" + itemAt.Template.Pic));
			string text2 = text;
			string text3 = text;
			for (int i = 0; i < StyleIndex.Length; i++)
			{
				text2 += ",";
				text3 += ",";
				if (StyleIndex[i] == 11)
				{
					if (PlayerCharacter.Sex)
					{
						ItemTemplateInfo itemTemplateInfo = ItemMgr.FindItemTemplate(13573);
						object obj = text2;
						text2 = string.Concat(obj, itemTemplateInfo.TemplateID, "|", itemTemplateInfo.Pic);
						itemTemplateInfo = ItemMgr.FindItemTemplate(13572);
						object obj2 = text3;
						text3 = string.Concat(obj2, itemTemplateInfo.TemplateID, "|", itemTemplateInfo.Pic);
					}
					else
					{
						ItemTemplateInfo itemTemplateInfo2 = ItemMgr.FindItemTemplate(13575);
						object obj3 = text2;
						text2 = string.Concat(obj3, itemTemplateInfo2.TemplateID, "|", itemTemplateInfo2.Pic);
						itemTemplateInfo2 = ItemMgr.FindItemTemplate(13574);
						object obj4 = text3;
						text3 = string.Concat(obj4, itemTemplateInfo2.TemplateID, "|", itemTemplateInfo2.Pic);
					}
				}
				else if (StyleIndex[i] == 6)
				{
					ItemTemplateInfo itemTemplateInfo3 = ItemMgr.FindItemTemplate(70396);
					string text4 = itemTemplateInfo3.TemplateID + "|" + itemTemplateInfo3.Pic;
					text2 += text4;
					text3 += text4;
				}
				else
				{
					itemAt = GetItemAt(eBageType.EquipBag, StyleIndex[i]);
					if (itemAt != null)
					{
						string text5 = itemAt.TemplateID + "|" + itemAt.Pic;
						text2 += text5;
						text3 += text5;
					}
				}
			}
			return text2 + ";" + text3;
		}

		public void RemoveFightFootballStyle()
		{
			ItemInfo itemAt = GetItemAt(eBageType.EquipBag, 0);
			string text = ((itemAt == null) ? "" : (itemAt.TemplateID + "|" + itemAt.Template.Pic));
			for (int i = 0; i < StyleIndex.Length; i++)
			{
				text += ",";
				itemAt = GetItemAt(eBageType.EquipBag, StyleIndex[i]);
				if (itemAt != null)
				{
					object obj = text;
					text = string.Concat(obj, itemAt.TemplateID, "|", itemAt.Pic);
				}
			}
			if (!string.IsNullOrEmpty(text))
			{
				PlayerCharacter.Style = text;
			}
			OnPropertiesChanged();
		}

		public void UpdateProperties()
		{
			Out.SendUpdatePrivateInfo(m_character);
			GSPacketIn pkg = Out.SendUpdatePublicPlayer(m_character, m_battle.MatchInfo);
			if (m_currentRoom != null)
			{
				m_currentRoom.SendToAll(pkg, this);
			}
		}

		public int AddGold(int value)
		{
			if (value > 0)
			{
				m_character.Gold += value;
				if (m_character.Gold == int.MinValue)
				{
					m_character.Gold = int.MaxValue;
					SendMessage("O ouro atingiu seu limite, não pode mais receber.");
				}
				OnPropertiesChanged();
				return value;
			}
			return 0;
		}

		public int RemoveGold(int value)
		{
			if (value > 0 && value <= m_character.Gold)
			{
				m_character.Gold -= value;
				OnPropertiesChanged();
				return value;
			}
			return 0;
		}

		public int AddMoney(int value)
		{
			if (value > 0)
			{
				m_character.Money += value;
				if (m_character.Money == int.MinValue)
				{
					m_character.Money = int.MaxValue;
					SendMessage("Seus cupons atingiu o limite e não pode mais receber.");
				}
				OnPropertiesChanged();
				return value;
			}
			return 0;
		}

		public int AddHonor(int value)
		{
			if (value > 0)
			{
				m_character.myHonor += value;
				OnPropertiesChanged();
				return value;
			}
			return 0;
		}

		public int AddTotem(int value)
		{
			if (value <= 0)
			{
				return 0;
			}
			m_character.totemId += value;
			OnTotemLevelUp(value);
			OnPropertiesChanged();
			return value;
		}

		public int AddMaxHonor(int value)
		{
			if (value > 0)
			{
				m_character.MaxBuyHonor += value;
				OnPropertiesChanged();
				return value;
			}
			return 0;
		}

		public int RemoveMoney(int value, bool notify = true)
		{
			if (value <= 0 || value > m_character.Money)
			{
				return 0;
			}
			m_character.Money -= value;
			OnPropertiesChanged();
			if (notify)
			{
				if (value > 0 && Extra.CheckNoviceActiveOpen(NoviceActiveType.USE_MONEY_ACTIVE))
				{
					Extra.UpdateEventCondition(3, value, isPlus: true);
				}
				if (value > 0 && Extra.CheckNoviceActiveOpen(NoviceActiveType.CONSUME_DAY))
				{
					Extra.UpdateEventCondition(9, value, isPlus: true);
				}
				EveryDayActivePointMgr.ChangeUserActiveData(this, 62, value);
				NotifyConsumeMoney(value);
				if (PlayerCharacter.typeVIP > 0)
				{
					AddExpVip(value);
				}
			}
			return value;
		}

		public bool RemoveMissionEnergy(int value)
		{
			if (value > 0 && value <= Extra.Info.MissionEnergy)
			{
				UsersExtraInfo info = Extra.Info;
				info.MissionEnergy = info.MissionEnergy;
				Out.SendMissionEnergy(Extra.Info);
				return true;
			}
			return false;
		}

		public bool MissionEnergyEmpty(int value)
		{
			return value > Extra.Info.MissionEnergy;
		}

		public int AddMissionEnergy(int value)
		{
			if (value > 0)
			{
				Extra.Info.MissionEnergy += value;
				Out.SendMissionEnergy(Extra.Info);
				return value;
			}
			return 0;
		}

		public bool ActiveMoneyEnable(int value, bool notify = true)
		{
			if (!GameProperties.IsActiveMoney)
			{
				return MoneyDirect(value, notify);
			}
			if (value < 1 || value > int.MaxValue)
			{
				return false;
			}
			if (Actives.Info.ActiveMoney >= value)
			{
				RemoveActiveMoney(value);
				RemoveMoney(value, notify);
				return true;
			}
			SendMessage($"Energia não é suficiente.Atualmente você tem {Actives.Info.ActiveMoney}");
			return false;
		}

		public int AddActiveMoney(int value)
		{
			if (value > 0 && GameProperties.IsActiveMoney)
			{
				Actives.Info.ActiveMoney += value;
				if (Actives.Info.ActiveMoney == int.MinValue)
				{
					Actives.Info.ActiveMoney = int.MaxValue;
					SendMessage("A moeda dinâmica atingiu seu limite e não pode ser aceita.");
				}
				else
				{
					SendHideMessage($"O sistema acabou de adicionar {value} moedas dinâmicas, trazendo moedas dinâmicas para {Actives.Info.ActiveMoney} moedas");
				}
				return value;
			}
			return 0;
		}

		public int RemoveActiveMoney(int value)
		{
			if (value > 0 && value <= Actives.Info.ActiveMoney)
			{
				Actives.Info.ActiveMoney -= value;
				SendHideMessage($"Você acabou de consumir {value} moedas ativas e {Actives.Info.ActiveMoney} moedas ativas");
				return value;
			}
			return 0;
		}

		public int AddLeagueMoney(int value)
		{
			if (value > 0)
			{
				m_character.LeagueMoney += value;
				OnPropertiesChanged();
				return value;
			}
			return 0;
		}

		public int RemoveLeagueMoney(int value)
		{
			if (value > 0 && value <= m_character.LeagueMoney)
			{
				m_character.LeagueMoney -= value;
				OnPropertiesChanged();
				return value;
			}
			return 0;
		}

		public int AddMagicStonePoint(int value)
		{
			if (value > 0)
			{
				m_character.MagicStonePoint += value;
				Out.SendMagicStonePoint(PlayerCharacter);
				return value;
			}
			return 0;
		}

		public int RemoveMagicStonePoint(int value)
		{
			if (value > 0 && value <= m_character.MagicStonePoint)
			{
				m_character.MagicStonePoint -= value;
				Out.SendMagicStonePoint(PlayerCharacter);
				return value;
			}
			return 0;
		}

		public int AddGoXu(int value)
		{
			if (value > 0)
			{
				m_character.GoXu += value;
				SendMessage("Você Ganhou " + value + " GoXu. Suas Moédas GoXu Atual são: " + m_character.GoXu);
				return value;
			}
			return 0;
		}

		public int RemoveGoXu(int value)
		{
			return RemoveGoXu(value, notice: true);
		}

		public int RemoveGoXu(int value, bool notice)
		{
			if (value > 0 && value <= m_character.GoXu)
			{
				m_character.GoXu -= value;
				if (notice)
				{
					SendMessage("Bạn bị trừ " + value + " GoXu. GoXu hiện còn: " + m_character.GoXu);
				}
				return value;
			}
			return 0;
		}

		public int AddDDPlayPoint(int value)
		{
			if (value > 0)
			{
				m_character.DDPlayPoint += value;
				return value;
			}
			return 0;
		}

		public int RemoveDDPlayPoint(int value)
		{
			if (value > 0 && value <= m_character.DDPlayPoint)
			{
				m_character.DDPlayPoint -= value;
				return value;
			}
			return 0;
		}

		public int AddHardCurrency(int value)
		{
			if (value > 0)
			{
				m_character.hardCurrency += value;
				OnPropertiesChanged();
				return value;
			}
			return 0;
		}

		public int RemoveHardCurrency(int value)
		{
			if (value > 0 && value <= m_character.hardCurrency)
			{
				m_character.hardCurrency -= value;
				OnPropertiesChanged();
				return value;
			}
			return 0;
		}

		public void AddPrestige(bool isWin)
		{
			BattleData.AddPrestige(isWin);
		}

		public void UpdateHonor(string honor)
		{
			PlayerCharacter.Honor = honor;
			if (Rank.IsRank(honor))
			{
				EquipBag.UpdatePlayerProperties();
			}
		}

		public void UpdateRestCount()
		{
			BattleData.Update();
		}

		public void RemoveFistGetPet()
		{
			m_character.IsFistGetPet = false;
			m_character.LastRefreshPet = DateTime.Now.AddDays(-1.0);
		}

		public void RemoveLastRefreshPet()
		{
			m_character.LastRefreshPet = DateTime.Now;
		}

		public void UpdateAnswerSite(int id)
		{
			if (PlayerCharacter.AnswerSite < id)
			{
				PlayerCharacter.AnswerSite = id;
			}
			UpdateWeaklessGuildProgress();
			Out.SendWeaklessGuildProgress(PlayerCharacter);
		}

		public void UpdateWeaklessGuildProgress()
		{
			if (PlayerCharacter.weaklessGuildProgress == null)
			{
				PlayerCharacter.weaklessGuildProgress = Base64.decodeToByteArray(PlayerCharacter.WeaklessGuildProgressStr);
			}
			PlayerCharacter.CheckLevelFunction();
			if (PlayerCharacter.Grade == 1)
			{
				PlayerCharacter.openFunction(Step.GAIN_ADDONE);
			}
			if (PlayerCharacter.IsOldPlayer)
			{
				PlayerCharacter.openFunction(Step.OLD_PLAYER);
			}
			PlayerCharacter.WeaklessGuildProgressStr = Base64.encodeByteArray(PlayerCharacter.weaklessGuildProgress);
		}

		public bool canUpLv(int exp, int _curLv)
		{
			List<int> list = GameProperties.VIPExp();
			return (exp >= list[0] && _curLv == 0) || (exp >= list[1] && _curLv == 1) || (exp >= list[2] && _curLv == 2) || (exp >= list[3] && _curLv == 3) || (exp >= list[4] && _curLv == 4) || (exp >= list[5] && _curLv == 5) || (exp >= list[6] && _curLv == 6) || (exp >= list[7] && _curLv == 7) || (exp >= list[8] && _curLv == 8) || (exp >= list[9] && _curLv == 9) || (exp >= list[10] && _curLv == 10) || (exp >= list[11] && _curLv == 11);
		}

		public void AddExpVip(int value)
		{
			List<int> list = GameProperties.VIPExp();
			if (value >= 100)
			{
				m_character.VIPExp += value / 100;
			}
			for (int i = 0; i < list.Count; i++)
			{
				int vIPExp = m_character.VIPExp;
				int vIPLevel = m_character.VIPLevel;
				if (vIPLevel == 12)
				{
					m_character.VIPExp = list[11];
					break;
				}
				if (vIPLevel < 12 && canUpLv(vIPExp, vIPLevel))
				{
					m_character.VIPLevel++;
				}
			}
			OnPlayerVipLevelUpEvent(PlayerCharacter.VIPLevel);
			if (m_character.typeVIP > 0)
			{
				Out.SendOpenVIP(PlayerCharacter);
			}
			if (m_character.IsVIPExpire())
			{
				Out.SendOpenVIP(PlayerCharacter);
			}
		}

		public int AddCardSoul(int value)
		{
			if (value > 0)
			{
				m_character.CardSoul += value;
				if (m_character.CardSoul == int.MinValue)
				{
					m_character.CardSoul = int.MaxValue;
					SendMessage("A carta da alma atingiu o reino mais alto, não pode receber mais.");
				}
				return value;
			}
			return 0;
		}

		public int RemoveCardSoul(int value)
		{
			if (value > 0 && value <= m_character.CardSoul)
			{
				m_character.CardSoul -= value;
				return value;
			}
			return 0;
		}

		public int AddScore(int value)
		{
			if (value > 0)
			{
				m_character.Score += value;
				OnPropertiesChanged();
				return value;
			}
			return 0;
		}

		public int RemoveScore(int value)
		{
			if (value > 0 && value <= m_character.Score)
			{
				m_character.Score -= value;
				OnPropertiesChanged();
				return value;
			}
			return 0;
		}

		public int AddDamageScores(int value)
		{
			if (value > 0)
			{
				m_character.damageScores += value;
				if (m_character.damageScores == int.MinValue)
				{
					m_character.damageScores = int.MaxValue;
					SendMessage("A acumulação atingiu o reino mais alto, não pode receber mais.");
				}
				OnPropertiesChanged();
				return value;
			}
			return 0;
		}

		public int RemoveDamageScores(int value)
		{
			if (value > 0 && value <= m_character.damageScores)
			{
				m_character.damageScores -= value;
				OnPropertiesChanged();
				return value;
			}
			return 0;
		}

		public int AddPetScore(int value)
		{
			if (value > 0)
			{
				m_character.petScore += value;
				if (m_character.petScore == int.MinValue)
				{
					m_character.petScore = int.MaxValue;
					SendMessage("A acumulação atingiu o reino mais alto, não pode receber mais.");
				}
				OnPropertiesChanged();
				return value;
			}
			return 0;
		}

		public int RemovePetScore(int value)
		{
			if (value > 0 && value <= m_character.petScore)
			{
				m_character.petScore -= value;
				OnPropertiesChanged();
				return value;
			}
			return 0;
		}

		public int AddmyHonor(int value)
		{
			if (value > 0)
			{
				m_character.myHonor += value;
				OnPropertiesChanged();
				return value;
			}
			return 0;
		}

		public int RemovemyHonor(int value)
		{
			if (value > 0 && value <= m_character.myHonor)
			{
				m_character.myHonor -= value;
				OnPropertiesChanged();
				return value;
			}
			return 0;
		}

		public int AddMedal(int value)
		{
			if (value > 0)
			{
				m_character.medal += value;
				OnPropertiesChanged();
				return value;
			}
			return 0;
		}

		public int RemoveMedal(int value)
		{
			if (value > 0 && value <= m_character.medal)
			{
				m_character.medal -= value;
				OnPropertiesChanged();
				return value;
			}
			return 0;
		}

		public int AddOffer(int value)
		{
			return AddOffer(value, IsRate: true);
		}

		public int AddOffer(int value, bool IsRate)
		{
			if (value > 0)
			{
				if (AntiAddictionMgr.ISASSon)
				{
					value = (int)((double)value * AntiAddictionMgr.GetAntiAddictionCoefficient(PlayerCharacter.AntiAddiction));
				}
				if (IsRate)
				{
					value *= (((int)OfferAddPlus == 0) ? 1 : ((int)OfferAddPlus));
				}
				m_character.Offer += value;
				OnPropertiesChanged();
				return value;
			}
			return 0;
		}

		public int RemoveOffer(int value)
		{
			if (value > 0)
			{
				if (value >= m_character.Offer)
				{
					value = m_character.Offer;
				}
				m_character.Offer -= value;
				OnPropertiesChanged();
				return value;
			}
			return 0;
		}

		public int RemoveGiftToken(int value)
		{
			if (value > 0 && value <= m_character.GiftToken)
			{
				m_character.GiftToken -= value;
				OnPropertiesChanged();
				return value;
			}
			return 0;
		}

		public int AddGP(int gp)
		{
			if (gp >= 0)
			{
				if (AntiAddictionMgr.ISASSon)
				{
					gp = (int)((double)gp * AntiAddictionMgr.GetAntiAddictionCoefficient(PlayerCharacter.AntiAddiction));
				}
				gp = (int)((float)gp * RateMgr.GetRate(eRateType.Experience_Rate));
				if (GPAddPlus > 0.0)
				{
					gp = (int)((double)gp * GPAddPlus);
				}
				m_character.GP += gp;
				if (m_character.GP < 1)
				{
					m_character.GP = 1;
				}
				Level = LevelMgr.GetLevel(m_character.GP);
				int maxLevel = LevelMgr.MaxLevel;
				LevelInfo levelInfo = LevelMgr.FindLevel(maxLevel);
				if (Level == maxLevel && levelInfo != null)
				{
					m_character.GP = levelInfo.GP;
					int num = gp / 100;
					if (num > 0)
					{
						AddOffer(num);
						SendHideMessage(string.Format("", num));
					}
				}
				UpdateFightPower();
				OnPropertiesChanged();
				return gp;
			}
			return 0;
		}

		public void UpdateLevel()
		{
			Level = LevelMgr.GetLevel(m_character.GP);
			int maxLevel = LevelMgr.MaxLevel;
			LevelInfo levelInfo = LevelMgr.FindLevel(maxLevel);
			if (Level == maxLevel && levelInfo != null)
			{
				m_character.GP = levelInfo.GP;
			}
		}

		public int RemoveGP(int gp)
		{
			if (gp > 0)
			{
				m_character.GP -= gp;
				if (m_character.GP < 1)
				{
					m_character.GP = 1;
				}
				int level = LevelMgr.GetLevel(m_character.GP);
				if (Level > level)
				{
					m_character.GP += gp;
				}
				UpdateLevel();
				return gp;
			}
			return 0;
		}

		public int AddRobRiches(int value)
		{
			if (value > 0)
			{
				if (AntiAddictionMgr.ISASSon)
				{
					value = (int)((double)value * AntiAddictionMgr.GetAntiAddictionCoefficient(PlayerCharacter.AntiAddiction));
				}
				m_character.RichesRob += value;
				OnPropertiesChanged();
				return value;
			}
			return 0;
		}

		public int AddRichesOffer(int value)
		{
			if (value > 0)
			{
				m_character.RichesOffer += value;
				OnPropertiesChanged();
				return value;
			}
			return 0;
		}

		public int AddGiftToken(int value)
		{
			if (value > 0)
			{
				m_character.GiftToken += value;
				OnPropertiesChanged();
				return value;
			}
			return 0;
		}

		public bool CanEquip(ItemTemplateInfo item)
		{
			bool flag = true;
			string message = "";
			if (!item.CanEquip)
			{
				flag = false;
				message = LanguageMgr.GetTranslation("Game.Server.GameObjects.NoEquip");
			}
			else if (m_character.Grade < item.NeedLevel)
			{
				flag = false;
				message = LanguageMgr.GetTranslation("Game.Server.GameObjects.CanLevel");
			}
			if (!flag)
			{
				Out.SendMessage(eMessageType.ERROR, message);
			}
			return flag;
		}

		public void UpdateBaseProperties(int attack, int defence, int agility, int lucky, int magicAttack, int magicDefence, int hp)
		{
			if (attack != m_character.Attack || defence != m_character.Defence || agility != m_character.Agility || lucky != m_character.Luck || magicAttack != m_character.MagicAttack || magicDefence != m_character.MagicDefence)
			{
				m_character.Attack = attack;
				m_character.Defence = defence;
				m_character.Agility = agility;
				m_character.Luck = lucky;
				m_character.MagicAttack = magicAttack;
				m_character.MagicDefence = magicDefence;
				OnPropertiesChanged();
			}
			m_character.hp = (int)(((double)(hp + LevelPlusBlood + m_character.Defence / 10) + GetGoldBlood()) * GetBaseBlood());
		}

		public void UpdateStyle(string style, string colors, string skin)
		{
			if (style != m_character.Style || colors != m_character.Colors || skin != m_character.Skin)
			{
				m_character.Style = style;
				m_character.Colors = colors;
				m_character.Skin = skin;
				OnPropertiesChanged();
			}
		}

		public void UpdateFightPower()
		{
			int num = 0;
			FightPower = 0;
			int hp = PlayerCharacter.hp;
			num += PlayerCharacter.Attack;
			num += PlayerCharacter.Defence;
			num += PlayerCharacter.Agility;
			num += PlayerCharacter.Luck;
			num += PlayerCharacter.MagicAttack;
			num += PlayerCharacter.MagicDefence;
			double baseAttack = GetBaseAttack();
			double baseDefence = GetBaseDefence();
			FightPower += (int)((double)(num + 1000) * (baseAttack * baseAttack * baseAttack + 3.5 * baseDefence * baseDefence * baseDefence) / 100000000.0 + (double)hp * 0.95);
			if (m_currentSecondWeapon != null)
			{
				FightPower += (int)((double)m_currentSecondWeapon.Template.Property7 * Math.Pow(1.1, m_currentSecondWeapon.StrengthenLevel));
			}
			Extra.UpdateEventCondition(4, FightPower);
			PlayerCharacter.FightPower = FightPower;
		}

		public void UpdateHide(int hide)
		{
			if (hide != m_character.Hide)
			{
				m_character.Hide = hide;
				OnPropertiesChanged();
			}
		}

		public void UpdateWeapon(ItemInfo item)
		{
			if (item != m_MainWeapon)
			{
				m_MainWeapon = item;
				OnPropertiesChanged();
			}
		}

		public void UpdatePet(UsersPetinfo pet)
		{
			m_pet = pet;
		}

		public void UpdateHealstone(ItemInfo item)
		{
			if (item != null)
			{
				m_healstone = item;
			}
		}

		public void UpdateReduceDame(ItemInfo item)
		{
			if (item != null && item.Template != null)
			{
				PlayerCharacter.ReduceDamePlus = item.Template.Property1;
			}
		}

		public bool RemoveHealstone()
		{
			ItemInfo itemAt = m_equipBag.GetItemAt(18);
			return itemAt != null && itemAt.Count > 0 && m_equipBag.RemoveCountFromStack(itemAt, 1);
		}

		public void UpdateSecondWeapon(ItemInfo item)
		{
			if (item != m_currentSecondWeapon)
			{
				m_currentSecondWeapon = item;
				OnPropertiesChanged();
			}
		}

		public void HideEquip(int categoryID, bool hide)
		{
			if (categoryID >= 0 && categoryID < 10)
			{
				EquipShowImp(categoryID, (!hide) ? 1 : 2);
			}
		}

		public bool IsLimitCount(int count)
		{
			if (!GameProperties.IsLimitCount)
			{
				return false;
			}
			if (count > GameProperties.LimitCount)
			{
				SendMessage($"Số lượng quá lớn hảy nhập lại. Số lượng tối đa là {GameProperties.LimitCount}.");
				return true;
			}
			return false;
		}

		public bool IsLimitMoney(int count)
		{
			if (!GameProperties.IsLimitMoney)
			{
				return false;
			}
			if (count > GameProperties.LimitMoney)
			{
				SendMessage($"Số lượng Xu quá lớn hảy nhập lại. Tối đa là {GameProperties.LimitMoney}.");
				return true;
			}
			return false;
		}

		public bool IsLimitAuction()
		{
			if (!GameProperties.IsLimitAuction)
			{
				return false;
			}
			if (Extra.Info.FreeAddAutionCount >= GameProperties.LimitAuction)
			{
				SendMessage($"Số lần tham gia đấu giá hôm nay đã hết. Tối đa là {GameProperties.LimitAuction}.");
				return true;
			}
			Extra.Info.FreeAddAutionCount++;
			return false;
		}

		public bool IsLimitMail()
		{
			if (!GameProperties.IsLimitMail)
			{
				return false;
			}
			if (Extra.Info.FreeSendMailCount >= GameProperties.LimitMail)
			{
				SendMessage($"Số lần gửi mail hôm nay đã hết. Tối đa là {GameProperties.LimitMail}.");
				return true;
			}
			Extra.Info.FreeSendMailCount++;
			return false;
		}

		public void ApertureEquip(int level)
		{
			EquipShowImp(0, (level < 5) ? 1 : ((level < 7) ? 2 : 3));
		}

		private void EquipShowImp(int categoryID, int para)
		{
			UpdateHide((int)((double)m_character.Hide + Math.Pow(10.0, categoryID) * (double)(para - m_character.Hide / (int)Math.Pow(10.0, categoryID) % 10)));
		}

		public void LogAddMoney(AddMoneyType masterType, AddMoneyType sonType, int userId, int moneys, int SpareMoney)
		{
		}

		public bool Login()
		{
			if (WorldMgr.AddPlayer(m_character.ID, this))
			{
				try
				{
					if (LoadFromDatabase())
					{
						Out.SendLoginSuccess();
						Out.SendUpdatePublicPlayer(PlayerCharacter, BattleData.MatchInfo);
						Out.SendWeaklessGuildProgress(PlayerCharacter);
						Out.SendNecklaceStrength(PlayerCharacter);
						Out.SendMissionEnergy(Extra.Info);
						Out.SendUpdateOneKeyFinish(PlayerCharacter);
						Out.SendDateTime();
						Out.SendDailyAward(PlayerCharacter);
						LoadMarryMessage();
						if (!m_showPP)
						{
							m_playerProp.ViewCurrent();
							m_showPP = true;
						}
						if (PlayerCharacter.BoxGetDate.ToShortDateString() != DateTime.Now.ToShortDateString())
						{
							PlayerCharacter.AlreadyGetBox = 0;
							PlayerCharacter.BoxProgression = 0;
						}
						CheckSpeed = (long)DateTime.UtcNow.Subtract(new DateTime(1970, 1, 1)).TotalSeconds;
						int iD = PlayerCharacter.ID;
						Out.SendUserRanks(iD, Rank.GetRank());
						Out.SendLuckStoneEnable(iD);
						Out.SendActivityList(iD);
						Out.SendFindBackIncome(iD);
						Out.SendCryptBossInit(this);
						Out.SendPlayerDrill(iD, UserDrills);
						Out.SendOpenVIP(PlayerCharacter);
						Out.SendKingBlessMain(Extra);
						CreateDefaultDressModel();
						AvatarCollectionBag.UpdateInfo();
						Out.SendDressModelInfo(DressModel);
						Out.SendCurrentDressModel(PlayerCharacter);
						Out.SendEatPetsInfo(PlayerCharacter.EatPetInfo);
						m_warPassHandler = new WarPassProcessor(m_warPassProcessor);
						Out.SendLittleGameActivated(GameProperties.IsLittleGameOpen());
						BoxBeginTime = DateTime.Now;
						Out.SendOpenDDPlay(PlayerCharacter);
						SendPkgLimitGrate();
						if (GameProperties.IsPromotePackageOpen)
						{
							Out.SendOpenGrowthPackageOpen(iD);
							Out.SendGrowthPackageOpen(iD, Actives.Info.AvailTime);
						}
						Out.SendOpenBoguAdventure();
						Actives.SendEvent();
						Dice.SendDiceActiveOpen();
						EquipBag.UpdatePlayerProperties();
						GameServer.Instance.LoginServer.SendGetLightriddleInfo(iD);
						ProcessConsortiaAndPet();
						m_playerState = ePlayerState.Online;
						CheckLevelMagicStone();
						Extra.BeginPingOnlineTimer();
						Extra.BeginOnlineGameTimer();
						CheckNoviceActive();
						ChargeToUser();
						SendActivities();
						EveryDayActivePointMgr.SendEveryDayActiveDetail(this, EveryDayActiveDetail);
						int repute = PlayerCharacter.Repute;
						if (repute >= 1 && repute <= 10)
						{
							WorldMgr.SendSysNotice("O top " + PlayerCharacter.Repute + " do hall da fama [" + PlayerCharacter.NickName + "] acaba de entra no servidor! ");
						}
						return true;
					}
					WorldMgr.RemovePlayer(m_character.ID);
				}
				catch (Exception exception)
				{
					log.Error("Error Login!", exception);
				}
				return false;
			}
			return false;
		}

		public void ProcessConsortiaAndPet()
		{
			farmProcessor_0 = new FarmProcessor(m_farmProcessor);
			gypsyShopProcessor_0 = new GypsyShopProcessor(m_gypsyShopProcessor);
		}

		private void SendActivities()
		{
			Game.Server.EventSystem.EventSystem.OnLogin(this);
		}

		public void CreateDefaultDressModel()
		{
			List<ItemInfo> allEquipItems = EquipBag.GetAllEquipItems();
			if (allEquipItems.Count <= 0 || DressModel.GetDressModelWithSlotID(PlayerCharacter.CurrentDressModel).Count > 0)
			{
				return;
			}
			foreach (ItemInfo item in allEquipItems)
			{
				UserDressModelInfo userDressModelInfo = new UserDressModelInfo();
				userDressModelInfo.UserID = PlayerCharacter.ID;
				userDressModelInfo.ItemID = item.ItemID;
				userDressModelInfo.CategoryID = item.Template.CategoryID;
				userDressModelInfo.SlotID = PlayerCharacter.CurrentDressModel;
				userDressModelInfo.TemplateID = item.TemplateID;
				DressModel.AddDressModel(userDressModelInfo);
			}
		}

		public void CheckLevelMagicStone()
		{
			if (MagicStoneBag.GetItems().Count > 0 && PlayerCharacter.Grade < 40)
			{
				MagicStoneBag.ClearBag();
			}
		}

		public void LuckStarOpen()
		{
			if (!(GameProperties.LuckStarActivityEndDate < DateTime.Now))
			{
				GSPacketIn packet = new GSPacketIn(87, PlayerId);
				packet.WriteInt(25);
				SendTCP(packet);
			}
		}

		public void SendPkgLimitGrate()
		{
			LuckStarOpen();
			if (RoomMgr.WorldBossRoom.WorldbossOpen)
			{
				Out.SendOpenWorldBoss(X, Y);
			}
			int iD = PlayerCharacter.ID;
			//if (PlayerCharacter.Grade >= 15)
			//{
				//DEFINIR SE A LEAGUE ESTA ABERTA
				if (ActiveSystemMgr.IsLeagueOpen)
				{
					Out.SendLeagueNotice(iD, BattleData.MatchInfo.restCount, BattleData.maxCount, 1);
				}
				else
				{
					Out.SendLeagueNotice(iD, BattleData.MatchInfo.restCount, BattleData.maxCount, 2);
				}
				if (ActiveSystemMgr.IsFightFootballTime)
				{
					Out.SendFightFootballTimeOpenClose(iD, result: true);
				}
			//}
			if (PlayerCharacter.Grade >= 30)
			{
				Out.SendPlayerFigSpiritinit(iD, GemStone);
			}
			if (PlayerCharacter.Grade >= 15)
			{
				if (ActiveSystemMgr.IsBattleGoundOpen)
				{
					Out.SendBattleGoundOpen(iD);
				}
				if (Actives.IsDragonBoatOpen())
				{
					Out.SendDragonBoat(PlayerCharacter);
				}
				if (ActiveSystemMgr.LanternriddlesOpen)
				{
					Out.SendLanternriddlesOpen(iD, isOpen: true);
				}
			}
			if (Actives.IsYearMonsterOpen())
			{
				Out.SendCatchBeastOpen(iD, isOpen: true);
			}
		}

		public void LoadMarryMessage()
		{
			using (PlayerBussiness playerBussiness = new PlayerBussiness())
			{
				MarryApplyInfo[] playerMarryApply = playerBussiness.GetPlayerMarryApply(PlayerCharacter.ID);
				if (playerMarryApply == null)
				{
					return;
				}
				MarryApplyInfo[] array = playerMarryApply;
				foreach (MarryApplyInfo marryApplyInfo in array)
				{
					switch (marryApplyInfo.ApplyType)
					{
					case 1:
						Out.SendPlayerMarryApply(this, marryApplyInfo.ApplyUserID, marryApplyInfo.ApplyUserName, marryApplyInfo.LoveProclamation, marryApplyInfo.ID);
						break;
					case 2:
						Out.SendMarryApplyReply(this, marryApplyInfo.ApplyUserID, marryApplyInfo.ApplyUserName, marryApplyInfo.ApplyResult, isApplicant: true, marryApplyInfo.ID);
						if (!marryApplyInfo.ApplyResult)
						{
							Out.SendMailResponse(PlayerCharacter.ID, eMailRespose.Receiver);
						}
						break;
					case 3:
						Out.SendPlayerDivorceApply(this, result: true, isProposer: false);
						break;
					}
				}
			}
		}

		public string GetInventoryName(eBageType bageType)
		{
			switch (bageType)
			{
			case eBageType.EquipBag:
				return LanguageMgr.GetTranslation("Game.Server.GameObjects.Equip");
			case eBageType.PropBag:
				return LanguageMgr.GetTranslation("Game.Server.GameObjects.Prop");
			case eBageType.FightBag:
				return LanguageMgr.GetTranslation("Game.Server.GameObjects.FightBag");
			case eBageType.FarmBag:
				return LanguageMgr.GetTranslation("Game.Server.GameObjects.FarmBag");
			case eBageType.BeadBag:
				return LanguageMgr.GetTranslation("Game.Server.GameObjects.BeadBag");
			default:
				return bageType.ToString();
			}
		}

		public bool LoadFromDatabase()
		{
			using (PlayerBussiness playerBussiness = new PlayerBussiness())
			{
				PlayerInfo userSingleByUserID = playerBussiness.GetUserSingleByUserID(m_character.ID);
				if (userSingleByUserID == null)
				{
					Out.SendKitoff(LanguageMgr.GetTranslation("UserLoginHandler.Forbid"));
					Client.Disconnect();
					return false;
				}
				m_character = userSingleByUserID;
				m_character.Texp = playerBussiness.GetUserTexpInfoSingle(m_character.ID);
				m_character.DailyLogInfo = playerBussiness.GetDailyLogListSingle(m_character.ID) ?? new DailyLogListInfo
				{
					UserID = m_character.ID,
					UserAwardLog = 0,
					DayLog = "",
					LastDate = DateTime.Now.AddDays(-1.0),
					Times = 0
				};
				if (m_character.Texp.IsValidadteTexp())
				{
					m_character.Texp.texpCount = 0;
				}
				if (m_character.Grade > 19)
				{
					LoadGemStone(playerBussiness);
				}
				LoadDrills(playerBussiness);
				CheckSpeed = (long)DateTime.UtcNow.Subtract(new DateTime(1970, 1, 1)).TotalSeconds;
				PlayerCharacter.EatPetInfo = playerBussiness.GetUserEatPetsInfo(PlayerCharacter.ID, out var flag);
				if (!flag)
				{
					return false;
				}
				int[] updatedSlots = new int[3] { 0, 1, 2 };
				Out.SendUpdateInventorySlot(FightBag, updatedSlots);
				UpdateWeaklessGuildProgress();
				UpdateItemForUser(1);
				ChecVipkExpireDay();
				UpdateLevel();
				UpdatePet(m_petBag.GetPetIsEquip());
				EveryDayActivePointMgr.InitEveryDayActiveDetail(this);
				if (m_character.IsValidadteTimeBox())
				{
					m_character.TimeBox = DateTime.Now;
					m_character.receiebox = 0;
					m_character.MaxBuyHonor = 0;
					m_character.GetSoulCount = 30;
					m_farm.ResetFarmProp();
					m_battle.Reset();
					m_actives.ResetChristmas();
					m_actives.Info.activityTanabataNum = 0;
					m_extra.ResetUsersExtra();
					m_dice.Reset();
					m_actives.BoguAdventure.ResetCount = m_actives.countBoguReset;
					m_actives.BoguAdventure.Award = "0,0,0";
					m_actives.ResetBoguAdventureInfo();
					if (EveryDayActiveDetail != null)
					{
						EveryDayActiveDetail.ResetEveryDayActiveDetailInfo();
						DateTime date1 = EveryDayActiveDetail.LastLoginTime.AddDays(1.0).Date;
						DateTime date2 = DateTime.Now.Date;
						if (date1 == date2)
						{
							EveryDayActivePointMgr.ChangeUserActiveData(this, 1, 1);
						}
						EveryDayActivePointMgr.SendEveryDayActiveDetail(this, EveryDayActiveDetail);
						EveryDayActiveDetailInfo everyDayActiveDetail = EveryDayActiveDetail;
						DateTime date3 = DateTime.Now.Date;
						everyDayActiveDetail.LastLoginTime = date3;
					}
				}
				m_forcesWarPass.LoadFromDatabase();
				m_pvepermissions = (string.IsNullOrEmpty(m_character.PvePermission) ? InitPvePermission() : m_character.PvePermission.ToCharArray());
				LoadPvePermission();
				_friends = new Dictionary<int, int>();
				_friends = playerBussiness.GetFriendsIDAll(m_character.ID);
				_viFarms = new List<int>();
				m_character.State = 1;
				ClearHideBag();
				ClearCaddyBag();
				playerBussiness.UpdateUserTexpInfo(m_character.Texp);
				playerBussiness.UpdatePlayer(m_character);
				DayReset();
				SavePlayerInfo();
				return true;
			}
		}

		public void DayReset()
		{
			this.NewDayReachedEvent?.Invoke(this);
			if (PlayerCharacter.CheckNewDay())
			{
				if (m_character.accumulativeLoginDays < 7)
				{
					m_character.accumulativeLoginDays++;
				}
				Extra.ResetKingBlessData();
				ResetScoreEvent();
				m_extra.Info.CryptBossAward = Enumerable.Repeat(1, 6).ToArray();
				Extra.ResetNoviceEvent(NoviceActiveType.CONSUME_DAY);
				Extra.ResetNoviceEvent(NoviceActiveType.RECHANGE_MONEY_RESET_DAY);
				Extra.ResetNoviceEvent(NoviceActiveType.Time_online);
				PlayerCharacter.NewDay = DateTime.Now;
				SendMessage("Você alcançou um novo dia, suas atividades, eventos diários, missões e recargas foram resetas.");
			}
		}

		public void TestQuest()
		{
			using (ProduceBussiness produceBussiness = new ProduceBussiness())
			{
				QuestInfo[] aLlQuest = produceBussiness.GetALlQuest();
				QuestInfo[] array = aLlQuest;
				foreach (QuestInfo info in array)
				{
					QuestInventory.AddQuest(info, out var _);
				}
			}
		}

		public void SendConsortiaBossOpenClose(int type)
		{
			GSPacketIn gSPacketIn = new GSPacketIn(129, PlayerCharacter.ID);
			gSPacketIn.WriteByte(31);
			gSPacketIn.WriteByte((byte)type);
			SendTCP(gSPacketIn);
		}

		public void SendConsortiaBossInfo(ConsortiaInfo info)
		{
			RankingPersonInfo rankingPersonInfo = null;
			List<RankingPersonInfo> list = new List<RankingPersonInfo>();
			foreach (RankingPersonInfo value in info.RankList.Values)
			{
				if (value.Name == PlayerCharacter.NickName)
				{
					rankingPersonInfo = value;
				}
				else
				{
					list.Add(value);
				}
			}
			GSPacketIn gSPacketIn = new GSPacketIn(129, PlayerCharacter.ID);
			gSPacketIn.WriteByte(30);
			gSPacketIn.WriteByte((byte)info.bossState);
			gSPacketIn.WriteBoolean(rankingPersonInfo != null);
			if (rankingPersonInfo != null)
			{
				gSPacketIn.WriteInt(rankingPersonInfo.ID);
				gSPacketIn.WriteInt(rankingPersonInfo.TotalDamage);
				gSPacketIn.WriteInt(rankingPersonInfo.Honor);
				gSPacketIn.WriteInt(rankingPersonInfo.Damage);
			}
			gSPacketIn.WriteByte((byte)list.Count);
			foreach (RankingPersonInfo item in list)
			{
				gSPacketIn.WriteString(item.Name);
				gSPacketIn.WriteInt(item.ID);
				gSPacketIn.WriteInt(item.TotalDamage);
				gSPacketIn.WriteInt(item.Honor);
				gSPacketIn.WriteInt(item.Damage);
			}
			gSPacketIn.WriteByte((byte)info.extendAvailableNum);
			gSPacketIn.WriteDateTime(info.endTime);
			gSPacketIn.WriteInt(info.callBossLevel);
			SendTCP(gSPacketIn);
		}

		public GSPacketIn UpdateGoodsCount()
		{
			return Out.SendUpdateGoodsCount(PlayerCharacter, null, null);
		}

		public void LoadDrills(PlayerBussiness db)
		{
			m_userDrills = db.GetPlayerDrillByID(m_character.ID);
			if (m_userDrills.Count != 0)
			{
				return;
			}
			List<int> list = new List<int> { 13, 14, 15, 16, 17, 18 };
			List<int> list2 = new List<int> { 0, 1, 2, 3, 4, 5 };
			for (int i = 0; i < list.Count; i++)
			{
				UserDrillInfo userDrillInfo = new UserDrillInfo();
				userDrillInfo.UserID = m_character.ID;
				userDrillInfo.BeadPlace = list[i];
				userDrillInfo.HoleLv = 0;
				userDrillInfo.HoleExp = 0;
				userDrillInfo.DrillPlace = list2[i];
				db.AddUserUserDrill(userDrillInfo);
				if (!m_userDrills.ContainsKey(userDrillInfo.DrillPlace))
				{
					m_userDrills.Add(userDrillInfo.DrillPlace, userDrillInfo);
				}
			}
		}

		public void LoadGemStone(PlayerBussiness db)
		{
			m_GemStone = db.GetSingleGemStones(m_character.ID);
			if (m_GemStone.Count == 0)
			{
				List<int> list = new List<int> { 11, 5, 2, 3, 13 };
				List<int> list2 = new List<int> { 100002, 100003, 100001, 100004, 100005 };
				for (int i = 0; i < list.Count; i++)
				{
					UserGemStone userGemStone = new UserGemStone();
					userGemStone.ID = 0;
					userGemStone.UserID = m_character.ID;
					userGemStone.FigSpiritId = list2[i];
					userGemStone.FigSpiritIdValue = "0,0,0|0,0,1|0,0,2";
					userGemStone.EquipPlace = list[i];
					m_GemStone.Add(userGemStone);
					db.AddUserGemStone(userGemStone);
				}
			}
		}

		public UserGemStone GetGemStone(int place)
		{
			foreach (UserGemStone item in m_GemStone)
			{
				if (place == item.EquipPlace)
				{
					return item;
				}
			}
			return null;
		}

		private void CheckNoviceActive()
		{
			if (Extra.CheckNoviceActiveOpen(NoviceActiveType.GRADE_UP_ACTIVE))
			{
				EventRewardProcessInfo eventProcess = Extra.GetEventProcess(1);
				Out.SendOpenNoviceActive(0, 1, eventProcess.Conditions, eventProcess.AwardGot);
			}
			if (Extra.CheckNoviceActiveOpen(NoviceActiveType.PVP_Win_Game))
			{
				EventRewardProcessInfo eventProcess2 = Extra.GetEventProcess(2);
				Out.SendOpenNoviceActive(0, 2, eventProcess2.Conditions, eventProcess2.AwardGot);
			}
			if (Extra.CheckNoviceActiveOpen(NoviceActiveType.USE_MONEY_ACTIVE))
			{
				EventRewardProcessInfo eventProcess3 = Extra.GetEventProcess(3);
				Out.SendOpenNoviceActive(0, 3, eventProcess3.Conditions, eventProcess3.AwardGot);
			}
			if (Extra.CheckNoviceActiveOpen(NoviceActiveType.UPDATE_FIGHT_POWER_ACTIVE))
			{
				EventRewardProcessInfo eventProcess4 = Extra.GetEventProcess(4);
				Out.SendOpenNoviceActive(0, 4, eventProcess4.Conditions, eventProcess4.AwardGot);
			}
			if (Extra.CheckNoviceActiveOpen(NoviceActiveType.RECHANGE_MONEY_ACTIVE))
			{
				EventRewardProcessInfo eventProcess5 = Extra.GetEventProcess(5);
				Out.SendOpenNoviceActive(0, 5, eventProcess5.Conditions, eventProcess5.AwardGot);
			}
			if (Extra.CheckNoviceActiveOpen(NoviceActiveType.RECHANGE_MONEY_RESET_DAY))
			{
				EventRewardProcessInfo eventProcess6 = Extra.GetEventProcess(6);
				Out.SendOpenNoviceActive(0, 6, eventProcess6.Conditions, eventProcess6.AwardGot);
			}
			if (Extra.CheckNoviceActiveOpen(NoviceActiveType.BATTLE_FIGH_COMBAT_GUILD))
			{
				EventRewardProcessInfo eventProcess7 = Extra.GetEventProcess(7);
				Out.SendOpenNoviceActive(0, 7, eventProcess7.Conditions, eventProcess7.AwardGot);
			}
			if (Extra.CheckNoviceActiveOpen(NoviceActiveType.Time_online))
			{
				EventRewardProcessInfo eventProcess8 = Extra.GetEventProcess(7);
				Out.SendOpenNoviceActive(0, 8, eventProcess8.Conditions, eventProcess8.AwardGot);
			}
			if (Extra.CheckNoviceActiveOpen(NoviceActiveType.CONSUME_DAY))
			{
				EventRewardProcessInfo eventProcess9 = Extra.GetEventProcess(9);
				Out.SendOpenNoviceActive(0, 9, eventProcess9.Conditions, eventProcess9.AwardGot);
			}
			if (Extra.CheckNoviceActiveOpen(NoviceActiveType.STRENGTHEN_WEAPON_ACTIVE))
			{
				EventRewardProcessInfo eventProcess10 = Extra.GetEventProcess(10);
				Out.SendOpenNoviceActive(0, 10, eventProcess10.Conditions, eventProcess10.AwardGot);
			}
			if (Extra.CheckNoviceActiveOpen(NoviceActiveType.STRENGTHEN_clothing_ACTIVE))
			{
				EventRewardProcessInfo eventProcess11 = Extra.GetEventProcess(11);
				Out.SendOpenNoviceActive(0, 11, eventProcess11.Conditions, eventProcess11.AwardGot);
			}
			if (Extra.CheckNoviceActiveOpen(NoviceActiveType.STRENGTHEN_hat_ACTIVE))
			{
				EventRewardProcessInfo eventProcess12 = Extra.GetEventProcess(12);
				Out.SendOpenNoviceActive(0, 12, eventProcess12.Conditions, eventProcess12.AwardGot);
			}
			if (Extra.CheckNoviceActiveOpen(NoviceActiveType.STRENGTHEN_Sun_ACTIVE))
			{
				EventRewardProcessInfo eventProcess13 = Extra.GetEventProcess(13);
				Out.SendOpenNoviceActive(0, 13, eventProcess13.Conditions, eventProcess13.AwardGot);
			}
		}

		public void UpdateGemStone(int place, UserGemStone gem)
		{
			for (int i = 0; i < m_GemStone.Count; i++)
			{
				if (place == m_GemStone[i].EquipPlace)
				{
					m_GemStone[i] = gem;
					break;
				}
			}
		}

		public void UpdateItemForUser(object state)
		{
			m_extra.LoadFromDatabase();
			m_battle.LoadFromDatabase();
			m_equipBag.LoadFromDatabase();
			m_magicStoneBag.LoadFromDatabase();
			m_propBag.LoadFromDatabase();
			m_ConsortiaBag.LoadFromDatabase();
			m_BeadBag.LoadFromDatabase();
			m_farmBag.LoadFromDatabase();
			m_petBag.LoadFromDatabase();
			m_storeBag.LoadFromDatabase();
			m_cardBag.LoadFromDatabase();
			m_questInventory.LoadFromDatabase(m_character.ID);
			m_achievementInventory.LoadFromDatabase(m_character.ID);
			m_bufferList.LoadFromDatabase(m_character.ID);
			m_treasure.LoadFromDatabase();
			m_rank.LoadFromDatabase();
			m_dressmodel.LoadFromDatabase();
			m_farm.LoadFromDatabase();
			m_dice.LoadFromDatabase();
			m_actives.LoadFromDatabase();
			GmActivityInventory.LoadFromDatabase();
			m_avatarCollectionBag.LoadFromDatabase();
			using (PlayerBussiness pb = new PlayerBussiness())
			{
				Temple = pb.GetUserHomeTempPraticeData(PlayerCharacter.ID);
			}
		}

		public void ChecVipkExpireDay()
		{
			if (m_character.IsLastVIPPackTime())
			{
				m_character.CanTakeVipReward = true;
			}
			else
			{
				m_character.CanTakeVipReward = false;
			}
		}

		public void LastVIPPackTime()
		{
			m_character.LastVIPPackTime = DateTime.Now;
			m_character.CanTakeVipReward = false;
		}

		public void OpenVIP(int num, DateTime ExpireDayOut)
		{
			int vipLevel = m_character.VIPLevel;
			if (vipLevel < 6 && num == 180)
			{
				m_character.typeVIP = 1;
				m_character.VIPLevel = 6;
				m_character.VIPExp = 4000;
				m_character.VIPExpireDay = ExpireDayOut;
				m_character.VIPLastDate = DateTime.Now;
				m_character.VIPNextLevelDaysNeeded = 0;
				m_character.CanTakeVipReward = true;
			}
			else if (vipLevel < 4 && num == 90)
			{
				m_character.typeVIP = 1;
				m_character.VIPLevel = 4;
				m_character.VIPExp = 800;
				m_character.VIPExpireDay = ExpireDayOut;
				m_character.VIPLastDate = DateTime.Now;
				m_character.VIPNextLevelDaysNeeded = 0;
				m_character.CanTakeVipReward = true;
			}
			else
			{
				m_character.typeVIP = 1;
				m_character.VIPLevel = 1;
				m_character.VIPExp = 0;
				m_character.VIPExpireDay = ExpireDayOut;
				m_character.VIPLastDate = DateTime.Now;
				m_character.VIPNextLevelDaysNeeded = 0;
				m_character.CanTakeVipReward = true;
			}
		}

		public void ContinousVIP(int num, DateTime ExpireDayOut)
		{
			int vipLevel = m_character.VIPLevel;
			if (vipLevel < 6 && num == 180)
			{
				m_character.VIPLevel = 6;
				m_character.VIPExp = 4000;
				m_character.VIPExpireDay = ExpireDayOut;
			}
			else if (vipLevel < 4 && num == 90)
			{
				m_character.VIPLevel = 4;
				m_character.VIPExp = 800;
				m_character.VIPExpireDay = ExpireDayOut;
			}
			else
			{
				m_character.VIPExpireDay = ExpireDayOut;
			}
		}

		public UserLabyrinthInfo LoadLabyrinth()
		{
			if (m_Labyrinth == null)
			{
				using (PlayerBussiness playerBussiness = new PlayerBussiness())
				{
					m_Labyrinth = playerBussiness.GetSingleLabyrinth(m_character.ID);
					if (m_Labyrinth == null)
					{
						m_Labyrinth = new UserLabyrinthInfo();
						m_Labyrinth.UserID = m_character.ID;
						m_Labyrinth.myProgress = 0;
						m_Labyrinth.myRanking = 0;
						m_Labyrinth.completeChallenge = true;
						m_Labyrinth.isDoubleAward = false;
						m_Labyrinth.currentFloor = 1;
						m_Labyrinth.accumulateExp = 0;
						m_Labyrinth.remainTime = 0;
						m_Labyrinth.currentRemainTime = 0;
						m_Labyrinth.cleanOutAllTime = 0;
						m_Labyrinth.cleanOutGold = 50;
						m_Labyrinth.tryAgainComplete = true;
						m_Labyrinth.isInGame = false;
						m_Labyrinth.isCleanOut = false;
						m_Labyrinth.serverMultiplyingPower = false;
						m_Labyrinth.LastDate = DateTime.Now;
						m_Labyrinth.ProcessAward = InitProcessAward();
						playerBussiness.AddUserLabyrinth(m_Labyrinth);
					}
					else
					{
						ProcessLabyrinthAward = m_Labyrinth.ProcessAward;
					}
				}
			}
			return Labyrinth;
		}

		public string InitProcessAward()
		{
			string[] array = new string[99];
			for (int i = 0; i < array.Length; i++)
			{
				array[i] = i.ToString();
			}
			ProcessLabyrinthAward = string.Join("-", array);
			return ProcessLabyrinthAward;
		}

		public string CompleteGetAward(int floor)
		{
			string[] array = new string[floor];
			for (int i = 0; i < floor; i++)
			{
				array[i] = "i";
			}
			string[] array2 = m_Labyrinth.ProcessAward.Split('-');
			string text = string.Join("-", array);
			for (int j = floor; j < array2.Length; j++)
			{
				text = text + "-" + array2[j];
			}
			return text;
		}

		public bool isDoubleAward()
		{
			return m_Labyrinth != null && m_Labyrinth.isDoubleAward;
		}

		public void OutLabyrinth(bool isWin)
		{
			if (!isWin && m_Labyrinth != null && m_Labyrinth.currentFloor > 1)
			{
				SendLabyrinthTryAgain();
			}
			ResetLabyrinth();
		}

		public void ResetLabyrinth()
		{
			if (m_Labyrinth != null)
			{
				m_Labyrinth.isInGame = false;
				m_Labyrinth.completeChallenge = false;
				m_Labyrinth.ProcessAward = InitProcessAward();
			}
		}

		public void CalculatorClearnOutLabyrinth()
		{
			if (m_Labyrinth != null)
			{
				int num = 0;
				for (int i = m_Labyrinth.currentFloor; i <= m_Labyrinth.myProgress; i++)
				{
					num += 2;
				}
				num *= 60;
				m_Labyrinth.remainTime = num;
				m_Labyrinth.currentRemainTime = num;
				m_Labyrinth.cleanOutAllTime = num;
			}
		}

		public int[] CreateExps()
		{
			int[] array = new int[40];
			int num = 660;
			for (int i = 0; i < array.Length; i++)
			{
				array[i] = num;
				num += 690;
			}
			return array;
		}

		public void UpdateLabyrinth(int floor, int m_missionInfoId, bool bigAward)
		{
			int[] array = CreateExps();
			int num = ((floor - 1 > array.Length) ? (array.Length - 1) : (floor - 1));
			num = ((num >= 0) ? num : 0);
			int num2 = array[num];
			string text = labyrinthGolds[num];
			int num3 = int.Parse(text.Split('|')[0]);
			int num4 = int.Parse(text.Split('|')[1]);
			if (m_Labyrinth != null)
			{
				floor++;
				ProcessLabyrinthAward = CompleteGetAward(floor);
				m_Labyrinth.ProcessAward = ProcessLabyrinthAward;
				ItemInfo itemByTemplateID = PropBag.GetItemByTemplateID(0, 11916);
				if (itemByTemplateID == null || !RemoveTemplate(11916, 1))
				{
					m_Labyrinth.isDoubleAward = false;
				}
				if (m_Labyrinth.isDoubleAward)
				{
					int num5 = 2;
					num2 *= num5;
					num3 *= num5;
					num4 *= num5;
				}
				if (floor > m_Labyrinth.myProgress)
				{
					m_Labyrinth.myProgress = floor;
				}
				if (floor > m_Labyrinth.currentFloor)
				{
					m_Labyrinth.currentFloor = floor;
				}
				m_Labyrinth.accumulateExp += num2;
				string text2 = $"Você Ganhou: {num2} exp";
				AddGP(num2);
				if (bigAward)
				{
					List<ItemInfo> list = CopyDrop(2, 40002);
					if (list != null)
					{
						foreach (ItemInfo item in list)
						{
							item.IsBinds = true;
							AddTemplate(item, item.Template.BagType, num3, eGameView.dungeonTypeGet);
							text2 += $", {item.Template.Name} x{num3}";
						}
					}
					AddHardCurrency(num4);
					text2 = text2 + ", Desconhecido x" + num4;
				}
				SendHideMessage(text2);
			}
			Out.SendLabyrinthUpdataInfo(m_Labyrinth.UserID, m_Labyrinth);
		}

		public List<ItemInfo> CopyDrop(int SessionId, int m_missionInfoId)
		{
			List<ItemInfo> info = null;
			DropInventory.CopyDrop(m_missionInfoId, SessionId, ref info);
			return info;
		}

		public bool MoneyDirect(int value, bool notify = true)
		{
			if (GameProperties.IsDDTMoneyActive)
			{
				return MoneyDirect(MoneyType.DDTMoney, value, notify);
			}
			return MoneyDirect(MoneyType.Money, value, notify);
		}

		public bool MoneyDirect(MoneyType type, int value, bool notify = true)
		{
			if (value < 0 || value > int.MaxValue)
			{
				return false;
			}
			if (type == MoneyType.Money)
			{
				if (PlayerCharacter.Money >= value)
				{
					RemoveMoney(value, notify);
					return true;
				}
				SendInsufficientMoney(0);
			}
			else
			{
				if (PlayerCharacter.GiftToken >= value)
				{
					RemoveGiftToken(value);
					return true;
				}
				SendMessage("Moedas insuficientes, operação falhou.");
			}
			return false;
		}

		public bool SavePlayerInfo()
		{
			try
			{
				if (m_character.IsDirty)
				{
					using (PlayerBussiness playerBussiness = new PlayerBussiness())
					{
						playerBussiness.UpdatePlayer(m_character);
					}
				}
				return true;
			}
			catch (Exception exception)
			{
				log.Error("Error saving player info of " + m_character.UserName + "!", exception);
				return false;
			}
		}

		public bool SaveIntoDatabase()
		{
			try
			{
				using (PlayerBussiness playerBussiness = new PlayerBussiness())
				{
					if (m_character.IsDirty)
					{
						playerBussiness.UpdatePlayer(m_character);
						if (m_Labyrinth != null)
						{
							playerBussiness.UpdateLabyrinthInfo(m_Labyrinth);
						}
						foreach (UserDrillInfo value in m_userDrills.Values)
						{
							playerBussiness.UpdateUserDrillInfo(value);
						}
						foreach (UserGemStone item in m_GemStone)
						{
							playerBussiness.UpdateGemStoneInfo(item);
						}
						if (Temple.IsDirty)
						{
							playerBussiness.UpdateUserHomeTempPraticeData(Temple);
						}
					}
					DailyLogListInfo dailyLogInfo = m_character.DailyLogInfo;
					if (dailyLogInfo != null && dailyLogInfo.IsDirty)
					{
						playerBussiness.UpdateDailyLogList(m_character.DailyLogInfo);
					}
					UserEatPetsInfo eatPetInfo = PlayerCharacter.EatPetInfo;
					if (eatPetInfo != null && (eatPetInfo.IsDirty ? true : false))
					{
						playerBussiness.UpdateUserEatPetsInfo(PlayerCharacter.EatPetInfo);
					}
					EquipBag.SaveToDatabase();
					PropBag.SaveToDatabase();
					ConsortiaBag.SaveToDatabase();
					BeadBag.SaveToDatabase();
					MagicStoneBag.SaveToDatabase();
					FarmBag.SaveToDatabase();
					PetBag.SaveToDatabase(saveAdopt: true);
					CardBag.SaveToDatabase();
					StoreBag.SaveToDatabase();
					Farm.SaveToDatabase();
					Treasure.SaveToDatabase();
					Rank.SaveToDatabase();
					DressModel.SaveToDatabase();
					AvatarCollectionBag.SaveToDatabase();
					QuestInventory.SaveToDatabase();
					AchievementInventory.SaveToDatabase();
					BufferList.SaveToDatabase();
					BattleData.SaveToDatabase();
					Actives.SaveToDatabase();
					Dice.SaveToDatabase();
					Extra.SaveToDatabase();
					GmActivityInventory.SaveToDatabase();
					ForcesWarPass.SaveToDatabase();
					new EveryDayActivePointBussiness().SaveEveryDayActive(EveryDayActiveDetail);
					return true;
				}
			}
			catch (Exception exception)
			{
				log.Error("Error saving player " + m_character.NickName + "!", exception);
				return false;
			}
		}

		public virtual bool Quit()
		{
			try
			{
				try
				{
					if (CurrentRoom != null)
					{
						CurrentRoom.RemovePlayerUnsafe(this);
						CurrentRoom = null;
					}
					else
					{
						RoomMgr.WaitingRoom.RemovePlayer(this);
					}
					if (CurrentMarryRoom != null)
					{
						CurrentMarryRoom.RemovePlayer(this);
						CurrentMarryRoom = null;
					}
					if (m_currentSevenDoubleRoom != null)
					{
						CurrentSevenDoubleRoom.RemovePlayer(this);
						CurrentSevenDoubleRoom = null;
					}
					RoomMgr.WorldBossRoom.RemovePlayer(this);
					RoomMgr.ChristmasRoom.SetMonterDie(PlayerCharacter.ID);
					RoomMgr.ChristmasRoom.RemovePlayer(this);
					RoomMgr.ConsBatRoom.RemovePlayer(this);
					RoomMgr.CampBattleRoom.RemovePlayer(this);
					Actives.StopChristmasTimer();
					Actives.StopLabyrinthTimer();
					Actives.StopLightriddleTimer();
					Extra.StopAllTimer();
					ClearHideBag();
					if (LittleGameInfo.ID != 0)
					{
						LittleGameWorldMgr.RemovePlayer(this);
					}
					if (LotteryAwardList.Count > 0 && Lottery != -1)
					{
						SendItemsToMail(LotteryAwardList, "", LanguageMgr.GetTranslation("Game.Server.Lottery.Oversea.MailTitle"), eMailType.BuyItem);
						ResetLottery();
					}
				}
				catch (Exception exception)
				{
					log.Error("Player exit Game Error!", exception);
				}
				m_character.State = 0;
				SaveIntoDatabase();
			}
			catch (Exception exception2)
			{
				log.Error("Player exit Error!!!", exception2);
			}
			finally
			{
				WorldMgr.RemovePlayer(m_character.ID);
			}
			return true;
		}

		public void ViFarmsAdd(int playerID)
		{
			if (!_viFarms.Contains(playerID))
			{
				_viFarms.Add(playerID);
			}
		}

		public void ViFarmsRemove(int playerID)
		{
			if (_viFarms.Contains(playerID))
			{
				_viFarms.Remove(playerID);
			}
		}

		public void FriendsAdd(int playerID, int relation)
		{
			if (!_friends.ContainsKey(playerID))
			{
				_friends.Add(playerID, relation);
			}
			else
			{
				_friends[playerID] = relation;
			}
		}

		public void FriendsRemove(int playerID)
		{
			if (_friends.ContainsKey(playerID))
			{
				_friends.Remove(playerID);
			}
		}

		public bool IsBlackFriend(int playerID)
		{
			return _friends == null || (_friends.ContainsKey(playerID) && _friends[playerID] == 1);
		}

		public void ClearConsortia()
		{
			PlayerCharacter.ClearConsortia();
			OnPropertiesChanged();
			QuestInventory.ClearConsortiaQuest();
		}

		public bool UsePayBuff(BuffType type)
		{
			return false;
		}

		public int AddBadLuckCaddy(int value)
		{
			if (value <= 0)
			{
				return 0;
			}
			m_character.badLuckNumber += value;
			if (m_character.badLuckNumber == int.MinValue)
			{
				m_character.badLuckNumber = int.MaxValue;
				SendMessage("Limite de gemas excedido.");
			}
			OnPropertiesChanged();
			return value;
		}

		public void AddRuneProperty(ItemInfo item, ref double defence, ref double attack)
		{
			RuneTemplateInfo runeTemplateInfo = RuneMgr.FindRuneByTemplateID(item.TemplateID);
			if (runeTemplateInfo == null)
			{
				return;
			}
			string[] array = runeTemplateInfo.Attribute1.Split('|');
			string[] array2 = runeTemplateInfo.Attribute2.Split('|');
			int num = 0;
			int num2 = 0;
			if (item.Hole1 > runeTemplateInfo.BaseLevel)
			{
				if (array.Length > 1)
				{
					num = 1;
				}
				if (array2.Length > 1)
				{
					num2 = 1;
				}
			}
			int num3 = Convert.ToInt32(array[num]);
			Convert.ToInt32(array2[num2]);
			switch (runeTemplateInfo.Type1)
			{
			case 35:
				attack += num3;
				break;
			case 36:
				defence += num3;
				break;
			}
		}

		public double getHertAddition(double para1, double para2)
		{
			double a = para1 * Math.Pow(1.1, para2) - para1;
			return Math.Round(a);
		}

		public double GetBaseAttack()
		{
			double num = 0.0;
			double defence = 0.0;
			double attack = 0.0;
			double num2 = 0.0;
			double num3 = 0.0;
			UserRankInfo rank = Rank.GetRank(PlayerCharacter.Honor);
			if (rank != null)
			{
				num += (double)rank.Damage;
			}
			List<ItemInfo> allEquipItems = m_equipBag.GetAllEquipItems();
			foreach (ItemInfo item in allEquipItems)
			{
				SubActiveConditionInfo subActiveInfo = SubActiveMgr.GetSubActiveInfo(item);
				if (subActiveInfo != null)
				{
					num += (double)subActiveInfo.GetValue("6");
				}
			}
			PlayerProp.totalDamage = (int)num;
			for (int i = 0; i < 31; i++)
			{
				ItemInfo itemAt = m_BeadBag.GetItemAt(i);
				if (itemAt != null)
				{
					AddRuneProperty(itemAt, ref defence, ref attack);
				}
			}
			PlayerProp.UpadateBaseProp(isSelf: true, "Damage", "Bead", attack);
			PlayerProp.UpadateBaseProp(isSelf: true, "Damage", "Suit", num2);
			List<UsersCardInfo> cards = m_cardBag.GetCards(0, 5);
			foreach (UsersCardInfo item2 in cards)
			{
				if (item2.CardID != 0)
				{
					CardTemplateInfo cardTemplateInfo = CardMgr.FindCardTemplate(item2.TemplateID, item2.CardType);
					if (cardTemplateInfo != null)
					{
						num += (double)cardTemplateInfo.AddDamage;
					}
					num += (double)item2.Damage;
				}
			}
			BaseAttributes AvatarCollectionAttributes = AvatarCollectionBag.GetAttributes();
			num += (double)AvatarCollectionAttributes.Damage;
			PlayerProp.UpadateBaseProp(isSelf: true, "Damage", "Avatar", num3);
			num += (double)TotemMgr.GetTotemProp(m_character.totemId, "dam");
			ItemInfo itemAt2 = m_equipBag.GetItemAt(6);
			if (itemAt2 != null)
			{
				double num5 = itemAt2.Template.Property7;
				int num6 = (itemAt2.IsGold ? 1 : 0);
				double para = itemAt2.StrengthenLevel + num6;
				num += getHertAddition(num5, para) + num5;
			}
			return num + attack + num2 + num3;
		}

		public void PVEFightMessage(string translation, ItemInfo itemInfo, int areaID)
		{
			if (translation != null)
			{
				GameServer.Instance.LoginServer.SendPacket(WorldMgr.SendSysNotice(eMessageType.ChatNormal, translation, (itemInfo.ItemID == 0) ? 1 : itemInfo.ItemID, itemInfo.TemplateID, "", Client.Player.ZoneId));
			}
		}

		public double GetBaseDefence()
		{
			double num = 0.0;
			double defence = 0.0;
			double num2 = 0.0;
			double num3 = 0.0;
			double attack = 0.0;
			UserRankInfo rank = Rank.GetRank(PlayerCharacter.Honor);
			if (rank != null)
			{
				num += (double)rank.Guard;
			}
			SetsBuildTempMgr.GetSetsBuildProp(PlayerCharacter.fineSuitExp, ref num);
			List<ItemInfo> allEquipItems = m_equipBag.GetAllEquipItems();
			foreach (ItemInfo item in allEquipItems)
			{
				SubActiveConditionInfo subActiveInfo = SubActiveMgr.GetSubActiveInfo(item);
				if (subActiveInfo != null)
				{
					num += (double)subActiveInfo.GetValue("7");
				}
			}
			if (Pet?.EquipList != null)
			{
				foreach (PetEquipDataInfo equip in Pet.EquipList)
				{
					if ((equip == null || equip.eqTemplateID > 0) && equip.eqType == 1)
					{
						PetMoeProperty petMoeProperty = PetExtraMgr.FindPetMoeProperty(PlayerCharacter.EatPetInfo.HatLevel);
						if (petMoeProperty != null)
						{
							num3 += (double)petMoeProperty.Guard;
						}
					}
				}
			}
			PlayerProp.totalArmor = (int)num;
			for (int i = 0; i < 31; i++)
			{
				ItemInfo itemAt = m_BeadBag.GetItemAt(i);
				if (itemAt != null)
				{
					AddRuneProperty(itemAt, ref defence, ref attack);
				}
			}
			PlayerProp.UpadateBaseProp(isSelf: true, "Armor", "Bead", defence);
			PlayerProp.UpadateBaseProp(isSelf: true, "Armor", "Suit", num2);
			BaseAttributes AvatarCollectionAttributes = AvatarCollectionBag.GetAttributes();
			defence += (double)AvatarCollectionAttributes.Armor;
			PlayerProp.UpadateBaseProp(isSelf: true, "Armor", "Avatar", num3);
			List<UsersCardInfo> cards = m_cardBag.GetCards(0, 5);
			foreach (UsersCardInfo item3 in cards)
			{
				if (item3.CardID > 0)
				{
					CardTemplateInfo cardTemplateInfo = CardMgr.FindCardTemplate(item3.TemplateID, item3.CardType);
					if (cardTemplateInfo != null)
					{
						num += (double)cardTemplateInfo.AddGuard;
					}
					num += (double)item3.Guard;
				}
			}
			num += (double)TotemMgr.GetTotemProp(m_character.totemId, "gua");
			ItemInfo itemAt2 = m_equipBag.GetItemAt(0);
			if (itemAt2 != null)
			{
				double num5 = itemAt2.Template.Property7;
				int num6 = (itemAt2.IsGold ? 1 : 0);
				double para = itemAt2.StrengthenLevel + num6;
				num += getHertAddition(num5, para) + num5;
			}
			ItemInfo itemAt3 = m_equipBag.GetItemAt(4);
			if (itemAt3 != null)
			{
				double num7 = itemAt3.Template.Property7;
				int num8 = (itemAt3.IsGold ? 1 : 0);
				double para2 = itemAt3.StrengthenLevel + num8;
				num += getHertAddition(num7, para2) + num7;
			}
			HomeTempPracticeInfo templeInfo = HomeTempleMgr.FindHomeTempPractice(Temple?.CurrentLevel ?? 0);
			return num + defence + num2 + num3 + (double)templeInfo.Guard;
		}

		public double GetBaseAgility()
		{
			return Math.Max((int)Math.Round(1600.0 - 1200.0 * (double)m_character.Agility / ((double)m_character.Agility + 1200.0)), 0);
		}

		public double GetBaseBlood()
		{
			ItemInfo itemAt = EquipBag.GetItemAt(12);
			if (itemAt != null)
			{
				return (100.0 + (double)itemAt.Template.Property1 + (double)PlayerCharacter.necklaceExpAdd) / 100.0;
			}
			return 1.0;
		}

		public double GetGoldBlood()
		{
			ItemInfo itemAt = EquipBag.GetItemAt(0);
			ItemInfo itemAt2 = EquipBag.GetItemAt(4);
			double num = 0.0;
			if (itemAt != null)
			{
				GoldEquipTemplateLoadInfo goldEquipTemplateLoadInfo = GoldEquipMgr.FindGoldEquipCategoryID(itemAt.Template.CategoryID);
				if (itemAt.IsGold)
				{
					num += (double)goldEquipTemplateLoadInfo.Boold;
				}
			}
			if (itemAt2 != null)
			{
				GoldEquipTemplateLoadInfo goldEquipTemplateLoadInfo2 = GoldEquipMgr.FindGoldEquipCategoryID(itemAt2.Template.CategoryID);
				if (itemAt2.IsGold)
				{
					num += (double)goldEquipTemplateLoadInfo2.Boold;
				}
			}
			return num;
		}

		public bool RemoveAt(eBageType bagType, int place)
		{
			return GetInventory(bagType)?.RemoveItemAt(place) ?? false;
		}

		public void UpdateBarrier(int barrier, string pic)
		{
			if (CurrentRoom != null)
			{
				CurrentRoom.Pic = pic;
				CurrentRoom.barrierNum = barrier;
				CurrentRoom.currentFloor = barrier;
			}
		}

		public bool DeletePropItem(int place)
		{
			FightBag.RemoveItemAt(place);
			return true;
		}

		public bool UseKingBlessHelpStraw(eRoomType roomType)
		{
			if (roomType == eRoomType.Lanbyrinth || roomType == eRoomType.Dungeon)
			{
				if (Extra.UseKingBless(4))
				{
					return true;
				}
				if (BufferList.UserSaveLifeBuff())
				{
					return true;
				}
			}
			return false;
		}

		public bool UsePropItem(AbstractGame game, int bag, int place, int templateId, bool isLiving)
		{
			if (bag == 1)
			{
				if (PropItemMgr.PropBag.Contains(templateId) && place == -1)
				{
					ItemTemplateInfo itemTemplateInfo = PropItemMgr.FindFightingProp(templateId);
					if (isLiving && itemTemplateInfo != null)
					{
						OnUsingItem(itemTemplateInfo.TemplateID);
						if (place == -1 && CanUseProp)
						{
							return true;
						}
						ItemInfo itemAt = GetItemAt(eBageType.PropBag, place);
						if (itemAt != null && itemAt.IsValidItem() && itemAt.Count >= 0)
						{
							itemAt.Count--;
							UpdateItem(itemAt);
							return true;
						}
					}
				}
			}
			else
			{
				ItemInfo itemAt2 = GetItemAt(eBageType.FightBag, place);
				if (itemAt2.TemplateID == templateId)
				{
					OnUsingItem(itemAt2.TemplateID);
					return RemoveAt(eBageType.FightBag, place);
				}
			}
			return false;
		}

		public void Disconnect()
		{
			m_client.Disconnect();
		}

		public void SendTCP(GSPacketIn pkg)
		{
			if (m_client.IsConnected)
			{
				m_client.SendTCP(pkg);
			}
		}

		public void ClearFootballCard()
		{
			for (int i = 0; i < CardsTakeOut.Length; i++)
			{
				CardsTakeOut[i] = null;
			}
		}

		public void TakeFootballCard(CardInfo card)
		{
			List<ItemInfo> list = new List<ItemInfo>();
			for (int i = 0; i < CardsTakeOut.Length; i++)
			{
				if (card.place == i)
				{
					CardsTakeOut[i] = card;
					CardsTakeOut[i].IsTake = true;
					ItemTemplateInfo itemTemplateInfo = ItemMgr.FindItemTemplate(card.templateID);
					if (itemTemplateInfo != null)
					{
						list.Add(ItemInfo.CreateFromTemplate(itemTemplateInfo, card.count, 110));
					}
					takeoutCount--;
					break;
				}
			}
			if (list.Count <= 0)
			{
				return;
			}
			foreach (ItemInfo item in list)
			{
				AddTemplate(list);
			}
		}

		public void RemoveLotteryItems(int templateId, int count)
		{
			foreach (ItemBoxInfo lotteryItem in LotteryItems)
			{
				if (lotteryItem.TemplateId == templateId && lotteryItem.ItemCount == count)
				{
					LotteryItems.Remove(lotteryItem);
					break;
				}
			}
		}

		public void AddLog(string type, string content)
		{
			using (PlayerBussiness playerBussiness = new PlayerBussiness())
			{
				playerBussiness.AddUserLogEvent(PlayerCharacter.ID, PlayerCharacter.UserName, PlayerCharacter.NickName, type, content);
			}
		}

		public void ShowAllFootballCard()
		{
			for (int i = 0; i < CardsTakeOut.Length; i++)
			{
				if (CardsTakeOut[i] == null)
				{
					CardsTakeOut[i] = Card[i];
					if (takeoutCount > 0)
					{
						TakeFootballCard(Card[i]);
					}
				}
			}
		}

		public void OnAcademyEvent(GamePlayer friendly, int type)
		{
			if (this.AcademyEvent != null)
			{
				this.AcademyEvent(friendly, type);
			}
		}

		public void FootballTakeOut(bool isWin)
		{
			if (isWin)
			{
				canTakeOut = 2;
				takeoutCount = 2;
			}
			else
			{
				canTakeOut = 1;
				takeoutCount = 1;
			}
		}

		public void LoadMarryProp()
		{
			using (PlayerBussiness playerBussiness = new PlayerBussiness())
			{
				MarryProp marryProp = playerBussiness.GetMarryProp(PlayerCharacter.ID);
				PlayerCharacter.IsMarried = marryProp.IsMarried;
				PlayerCharacter.SpouseID = marryProp.SpouseID;
				PlayerCharacter.SpouseName = marryProp.SpouseName;
				PlayerCharacter.IsCreatedMarryRoom = marryProp.IsCreatedMarryRoom;
				PlayerCharacter.SelfMarryRoomID = marryProp.SelfMarryRoomID;
				PlayerCharacter.IsGotRing = marryProp.IsGotRing;
				Out.SendMarryProp(this, marryProp);
			}
		}

		public override string ToString()
		{
			return $"Id:{PlayerId} nickname:{PlayerCharacter.NickName} room:{CurrentRoom} ";
		}

		public int ConsortiaFight(int consortiaWin, int consortiaLose, Dictionary<int, Player> players, eRoomType roomType, eGameType gameClass, int totalKillHealth, int count)
		{
			return ConsortiaMgr.ConsortiaFight(consortiaWin, consortiaLose, players, roomType, gameClass, totalKillHealth, count);
		}

		public void SendConsortiaFight(int consortiaID, int riches, string msg)
		{
			GSPacketIn gSPacketIn = new GSPacketIn(158);
			gSPacketIn.WriteInt(consortiaID);
			gSPacketIn.WriteInt(riches);
			gSPacketIn.WriteString(msg);
			GameServer.Instance.LoginServer.SendPacket(gSPacketIn);
		}

		public void LoadPvePermission()
		{
			PveInfo[] pveInfo = PveInfoMgr.GetPveInfo();
			PveInfo[] array = pveInfo;
			foreach (PveInfo pveInfo2 in array)
			{
				if (m_character.Grade > pveInfo2.LevelLimits)
				{
					bool flag = SetPvePermission(pveInfo2.ID, eHardLevel.Easy);
					if (flag)
					{
						flag = SetPvePermission(pveInfo2.ID, eHardLevel.Normal);
					}
					if (flag)
					{
						flag = SetPvePermission(pveInfo2.ID, eHardLevel.Hard);
					}
				}
			}
		}

		public char[] InitPvePermission()
		{
			char[] array = new char[50];
			for (int i = 0; i < array.Length; i++)
			{
				array[i] = '1';
			}
			return array;
		}

		public string ConverterPvePermission(char[] chArray)
		{
			string text = "";
			for (int i = 0; i < chArray.Length; i++)
			{
				text += chArray[i];
			}
			return text;
		}

		public bool SetPvePermission(int copyId, eHardLevel hardLevel)
		{
			if (hardLevel == eHardLevel.Epic)
			{
				return true;
			}
			if (copyId > m_pvepermissions.Length || copyId <= 0 || hardLevel == eHardLevel.Terror || m_pvepermissions[copyId - 1] != permissionChars[(int)hardLevel])
			{
				return true;
			}
			m_pvepermissions[copyId - 1] = permissionChars[(int)(hardLevel + 1)];
			m_character.PvePermission = ConverterPvePermission(m_pvepermissions);
			OnPropertiesChanged();
			return true;
		}

		public bool IsPvePermission(int copyId, eHardLevel hardLevel)
		{
			if (copyId > m_pvepermissions.Length || copyId <= 0)
			{
				return true;
			}
			if (hardLevel == eHardLevel.Epic)
			{
				return IsPveEpicPermission(copyId);
			}
			return m_pvepermissions[copyId - 1] >= permissionChars[(int)hardLevel];
		}

		public bool IsPveEpicPermission(int copyId)
		{
			string text = "1-2-3-4-5-6-7-8-9-10-11-12-13";
			bool result = false;
			if (text.Length > 0)
			{
				string[] array = text.Split('-');
				string[] array2 = array;
				foreach (string text2 in array2)
				{
					if (text2 == copyId.ToString())
					{
						result = true;
						break;
					}
				}
			}
			return result;
		}

		public void SendInsufficientMoney(int type)
		{
			GSPacketIn gSPacketIn = new GSPacketIn(88, PlayerId);
			gSPacketIn.WriteByte((byte)type);
			gSPacketIn.WriteBoolean(val: false);
			SendTCP(gSPacketIn);
		}

		public void SendMessage(eMessageType type, string msg)
		{
			GSPacketIn gSPacketIn = new GSPacketIn(3);
			gSPacketIn.WriteInt((int)type);
			gSPacketIn.WriteString(msg);
			SendTCP(gSPacketIn);
		}

		public void SendMessage(string msg)
		{
			GSPacketIn gSPacketIn = new GSPacketIn(3);
			gSPacketIn.WriteInt(0);
			gSPacketIn.WriteString(msg);
			SendTCP(gSPacketIn);
		}

		public void SendTranslatedMessage(string LanguageMsg, params object[] Params)
		{
			SendMessage(LanguageMgr.GetTranslation(LanguageMsg, Params));
		}

		public void SendPrivateChat(int receiverID, string receiver, string sender, string msg, bool isAutoReply)
		{
			GSPacketIn gSPacketIn = new GSPacketIn(37, PlayerCharacter.ID);
			gSPacketIn.WriteInt(receiverID);
			gSPacketIn.WriteString(receiver);
			gSPacketIn.WriteString(sender);
			gSPacketIn.WriteString(msg);
			gSPacketIn.WriteBoolean(isAutoReply);
			SendTCP(gSPacketIn);
		}

		public void SendHideMessage(string msg)
		{
			GSPacketIn gSPacketIn = new GSPacketIn(3);
			gSPacketIn.WriteInt(3);
			gSPacketIn.WriteString(msg);
			SendTCP(gSPacketIn);
		}

		public int LabyrinthTryAgainMoney()
		{
			for (int i = 0; i < Labyrinth.myProgress; i += 2)
			{
				if (Labyrinth.currentFloor == i)
				{
					return GameProperties.WarriorFamRaidPriceBig;
				}
			}
			return GameProperties.WarriorFamRaidPriceSmall;
		}

		public void SendLabyrinthTryAgain()
		{
			GSPacketIn gSPacketIn = new GSPacketIn(131, PlayerId);
			gSPacketIn.WriteByte(9);
			gSPacketIn.WriteInt(LabyrinthTryAgainMoney());
			SendTCP(gSPacketIn);
		}

		public bool SendItemsToMail(List<ItemInfo> items, string content, string title, eMailType type)
		{
			List<ItemInfo> list = new List<ItemInfo>();
			foreach (ItemInfo item in items)
			{
				item.BagType = -1;
				item.Place = -1;
				if (item.Template.MaxCount == 1)
				{
					for (int i = 0; i < item.Count; i++)
					{
						ItemInfo itemInfo = ItemInfo.CloneFromTemplate(item.Template, item);
						itemInfo.Count = 1;
						list.Add(itemInfo);
					}
				}
				else
				{
					list.Add(item);
				}
			}
			using (PlayerBussiness pb = new PlayerBussiness())
			{
				return SendItemsToMail(list, content, title, type, pb);
			}
		}

		public bool UpdatePlayer()
		{
			using (PlayerBussiness playerBussiness = new PlayerBussiness())
			{
				playerBussiness.UpdatePlayer(PlayerCharacter);
				return true;
			}
		}

		public bool SendItemsToMail(List<ItemInfo> items, string content, string title, eMailType type, PlayerBussiness pb)
		{
			bool result = true;
			for (int i = 0; i < items.Count; i += 5)
			{
				MailInfo mailInfo = new MailInfo
				{
					Title = (title ?? LanguageMgr.GetTranslation("Game.Server.GameUtils.Title")),
					Gold = 0,
					IsExist = true,
					Money = 0,
					Receiver = PlayerCharacter.NickName,
					ReceiverID = PlayerId,
					Sender = PlayerCharacter.NickName,
					SenderID = PlayerId,
					Type = (int)type,
					GiftToken = 0
				};
				List<ItemInfo> list = new List<ItemInfo>();
				StringBuilder stringBuilder = new StringBuilder();
				StringBuilder stringBuilder2 = new StringBuilder();
				stringBuilder.Append(LanguageMgr.GetTranslation("Game.Server.GameUtils.CommonBag.AnnexRemark"));
				content = ((content != null) ? LanguageMgr.GetTranslation(content) : "");
				int num = i;
				if (items.Count > num)
				{
					ItemInfo itemInfo = items[num];
					if (itemInfo.ItemID == 0)
					{
						pb.AddGoods(itemInfo);
					}
					if (itemInfo.IsDirty && itemInfo.ItemID > 0)
					{
						pb.UpdateGoods(itemInfo);
					}
					if (itemInfo.BagType >= 0)
					{
						list.Add(itemInfo);
					}
					if (title == null)
					{
						mailInfo.Title = itemInfo.Template.Name;
					}
					mailInfo.Annex1 = itemInfo.ItemID.ToString();
					mailInfo.Annex1Name = itemInfo.Template.Name;
					stringBuilder.Append("1、" + mailInfo.Annex1Name + "x" + itemInfo.Count + ";");
					stringBuilder2.Append("1、" + mailInfo.Annex1Name + "x" + itemInfo.Count + ";");
				}
				num = i + 1;
				if (items.Count > num)
				{
					ItemInfo itemInfo2 = items[num];
					if (itemInfo2.ItemID == 0)
					{
						pb.AddGoods(itemInfo2);
					}
					if (itemInfo2.IsDirty && itemInfo2.ItemID > 0)
					{
						pb.UpdateGoods(itemInfo2);
					}
					if (itemInfo2.BagType >= 0)
					{
						list.Add(itemInfo2);
					}
					mailInfo.Annex2 = itemInfo2.ItemID.ToString();
					mailInfo.Annex2Name = itemInfo2.Template.Name;
					stringBuilder.Append("2、" + mailInfo.Annex2Name + "x" + itemInfo2.Count + ";");
					stringBuilder2.Append("2、" + mailInfo.Annex2Name + "x" + itemInfo2.Count + ";");
				}
				num = i + 2;
				if (items.Count > num)
				{
					ItemInfo itemInfo3 = items[num];
					if (itemInfo3.ItemID == 0)
					{
						pb.AddGoods(itemInfo3);
					}
					if (itemInfo3.IsDirty && itemInfo3.ItemID > 0)
					{
						pb.UpdateGoods(itemInfo3);
					}
					if (itemInfo3.BagType >= 0)
					{
						list.Add(itemInfo3);
					}
					mailInfo.Annex3 = itemInfo3.ItemID.ToString();
					mailInfo.Annex3Name = itemInfo3.Template.Name;
					stringBuilder.Append("3、" + mailInfo.Annex3Name + "x" + itemInfo3.Count + ";");
					stringBuilder2.Append("3、" + mailInfo.Annex3Name + "x" + itemInfo3.Count + ";");
				}
				num = i + 3;
				if (items.Count > num)
				{
					ItemInfo itemInfo4 = items[num];
					if (itemInfo4.ItemID == 0)
					{
						pb.AddGoods(itemInfo4);
					}
					if (itemInfo4.IsDirty && itemInfo4.ItemID > 0)
					{
						pb.UpdateGoods(itemInfo4);
					}
					if (itemInfo4.BagType >= 0)
					{
						list.Add(itemInfo4);
					}
					mailInfo.Annex4 = itemInfo4.ItemID.ToString();
					mailInfo.Annex4Name = itemInfo4.Template.Name;
					stringBuilder.Append("4、" + mailInfo.Annex4Name + "x" + itemInfo4.Count + ";");
					stringBuilder2.Append("4、" + mailInfo.Annex4Name + "x" + itemInfo4.Count + ";");
				}
				num = i + 4;
				if (items.Count > num)
				{
					ItemInfo itemInfo5 = items[num];
					if (itemInfo5.ItemID == 0)
					{
						pb.AddGoods(itemInfo5);
					}
					if (itemInfo5.IsDirty && itemInfo5.ItemID > 0)
					{
						pb.UpdateGoods(itemInfo5);
					}
					if (itemInfo5.BagType >= 0)
					{
						list.Add(itemInfo5);
					}
					mailInfo.Annex5 = itemInfo5.ItemID.ToString();
					mailInfo.Annex5Name = itemInfo5.Template.Name;
					stringBuilder.Append("5、" + mailInfo.Annex5Name + "x" + itemInfo5.Count + ";");
					stringBuilder2.Append("5、" + mailInfo.Annex5Name + "x" + itemInfo5.Count + ";");
				}
				mailInfo.AnnexRemark = stringBuilder.ToString();
				mailInfo.Content = ((content != "") ? content : stringBuilder2.ToString());
				if (pb.SendMail(mailInfo))
				{
					foreach (ItemInfo item in list)
					{
						TakeOutItem(item);
					}
				}
				else
				{
					result = false;
				}
			}
			return result;
		}

		public bool SendItemToMail(int templateID, string content, string title)
		{
			ItemTemplateInfo itemTemplateInfo = ItemMgr.FindItemTemplate(templateID);
			if (itemTemplateInfo == null)
			{
				return false;
			}
			if (content == "")
			{
				content = itemTemplateInfo.Name + "x1";
			}
			ItemInfo itemInfo = ItemInfo.CreateFromTemplate(itemTemplateInfo, 1, 104);
			itemInfo.IsBinds = true;
			return SendItemToMail(itemInfo, content, title, eMailType.Active);
		}

		public bool SendItemToMail(ItemInfo item, string content, string title, eMailType type)
		{
			using (PlayerBussiness pb = new PlayerBussiness())
			{
				return SendItemToMail(item, pb, content, title, type);
			}
		}

		public bool SendItemToMail(ItemInfo item, PlayerBussiness pb, string content, string title, eMailType type)
		{
			MailInfo mailInfo = new MailInfo();
			mailInfo.Content = ((content != null) ? content : LanguageMgr.GetTranslation("Game.Server.GameUtils.Content"));
			mailInfo.Title = ((title != null) ? title : LanguageMgr.GetTranslation("Game.Server.GameUtils.Title"));
			mailInfo.Gold = 0;
			mailInfo.IsExist = true;
			mailInfo.Money = 0;
			mailInfo.GiftToken = 0;
			mailInfo.Receiver = PlayerCharacter.NickName;
			mailInfo.ReceiverID = PlayerCharacter.ID;
			mailInfo.Sender = PlayerCharacter.NickName;
			mailInfo.SenderID = PlayerCharacter.ID;
			mailInfo.Type = (int)type;
			if (item.ItemID == 0)
			{
				pb.AddGoods(item);
			}
			mailInfo.Annex1 = item.ItemID.ToString();
			mailInfo.Annex1Name = item.Template.Name;
			if (pb.SendMail(mailInfo))
			{
				TakeOutItem(item);
				return true;
			}
			return false;
		}

		public void OnVIPUpgrade(int level, int exp)
		{
			if (m_character.typeVIP > 0 && m_character.VIPLevel == level)
			{
			}
		}

		public byte SetTypeVIP(int days)
		{
			byte num = 1;
			if (days / 31 >= 3)
			{
				num = 2;
			}
			return num;
		}

		public bool SendMailToUser(PlayerBussiness pb, string content, string title, eMailType type)
		{
			MailInfo mailInfo = new MailInfo
			{
				Content = content,
				Title = title,
				Gold = 0,
				IsExist = true,
				Money = 0,
				GiftToken = 0,
				Receiver = PlayerCharacter.NickName,
				ReceiverID = PlayerCharacter.ID,
				Sender = PlayerCharacter.NickName,
				SenderID = PlayerCharacter.ID,
				Type = (int)type
			};
			mailInfo.Annex1 = "";
			mailInfo.Annex1Name = "";
			return pb.SendMail(mailInfo);
		}

		public bool SendMoneyMailToUser(string content, string title, int money, eMailType type)
		{
			MailInfo mail = new MailInfo
			{
				Content = content,
				Title = title,
				Gold = 0,
				IsExist = true,
				Money = money,
				GiftToken = 0,
				Receiver = PlayerCharacter.NickName,
				ReceiverID = PlayerCharacter.ID,
				Sender = PlayerCharacter.NickName,
				SenderID = PlayerCharacter.ID,
				Type = (int)type,
				Annex1 = "",
				Annex1Name = ""
			};
			using (PlayerBussiness pbs = new PlayerBussiness())
			{
				return pbs.SendMail(mail);
			}
		}

		public bool SendMoneyMailToUser(PlayerBussiness pb, string content, string title, int money, eMailType type)
		{
			MailInfo mailInfo = new MailInfo
			{
				Content = content,
				Title = title,
				Gold = 0,
				IsExist = true,
				Money = money,
				GiftToken = 0,
				Receiver = PlayerCharacter.NickName,
				ReceiverID = PlayerCharacter.ID,
				Sender = PlayerCharacter.NickName,
				SenderID = PlayerCharacter.ID,
				Type = (int)type
			};
			mailInfo.Annex1 = "";
			mailInfo.Annex1Name = "";
			return pb.SendMail(mailInfo);
		}

		public bool TakeOutItem(ItemInfo item)
		{
			if (item.BagType == m_propBag.BagType)
			{
				return m_propBag.TakeOutItem(item);
			}
			if (item.BagType == m_fightBag.BagType)
			{
				return m_fightBag.TakeOutItem(item);
			}
			if (item.BagType == m_ConsortiaBag.BagType)
			{
				return m_ConsortiaBag.TakeOutItem(item);
			}
			if (item.BagType == m_BeadBag.BagType)
			{
				return m_BeadBag.TakeOutItem(item);
			}
			if (item.BagType == m_magicStoneBag.BagType)
			{
				return m_magicStoneBag.TakeOutItem(item);
			}
			return m_equipBag.TakeOutItem(item);
		}

		public bool RemoveCountFromStack(ItemInfo item, int count)
		{
			if (item.BagType == m_propBag.BagType)
			{
				return m_propBag.RemoveCountFromStack(item, count);
			}
			if (item.BagType == m_ConsortiaBag.BagType)
			{
				return m_ConsortiaBag.RemoveCountFromStack(item, count);
			}
			if (item.BagType == m_BeadBag.BagType)
			{
				return m_BeadBag.RemoveCountFromStack(item, count);
			}
			if (item.BagType == m_magicStoneBag.BagType)
			{
				return m_magicStoneBag.RemoveCountFromStack(item, count);
			}
			return m_equipBag.RemoveCountFromStack(item, count);
		}

		public void AddGift(eGiftType type)
		{
			List<ItemInfo> list = new List<ItemInfo>();
			bool testActive = GameProperties.TestActive;
			switch (type)
			{
			case eGiftType.MONEY:
				if (testActive)
				{
					AddMoney(GameProperties.FreeMoney);
				}
				break;
			case eGiftType.SMALL_EXP:
			{
				string[] array2 = GameProperties.FreeExp.Split('|');
				ItemTemplateInfo itemTemplateInfo2 = ItemMgr.FindItemTemplate(Convert.ToInt32(array2[0]));
				if (itemTemplateInfo2 != null)
				{
					list.Add(ItemInfo.CreateFromTemplate(itemTemplateInfo2, Convert.ToInt32(array2[1]), 102));
				}
				break;
			}
			case eGiftType.BIG_EXP:
			{
				string[] array3 = GameProperties.BigExp.Split('|');
				ItemTemplateInfo itemTemplateInfo3 = ItemMgr.FindItemTemplate(Convert.ToInt32(array3[0]));
				if (itemTemplateInfo3 != null && testActive)
				{
					list.Add(ItemInfo.CreateFromTemplate(itemTemplateInfo3, Convert.ToInt32(array3[1]), 102));
				}
				break;
			}
			case eGiftType.PET_EXP:
			{
				string[] array = GameProperties.PetExp.Split('|');
				ItemTemplateInfo itemTemplateInfo = ItemMgr.FindItemTemplate(Convert.ToInt32(array[0]));
				if (itemTemplateInfo != null && testActive)
				{
					list.Add(ItemInfo.CreateFromTemplate(itemTemplateInfo, Convert.ToInt32(array[1]), 102));
				}
				break;
			}
			}
			foreach (ItemInfo item in list)
			{
				item.IsBinds = true;
				AddTemplate(item, item.Template.BagType, item.Count, eGameView.dungeonTypeGet);
			}
		}

		public void ResetScoreEvent()
		{
			Random random = new Random();
			if (m_character.Score <= 0 && m_character.damageScores <= 0)
			{
				return;
			}
			int num = m_character.Score / 100;
			int num2 = m_character.damageScores / 100;
			int num3 = random.Next(1, 3);
			string type = ((num3 == 1) ? "mérito" : "exp");
			if (num > 0 && m_character.Score > 0)
			{
				if (num3 == 1)
				{
					AddOffer(num);
				}
				if (num3 == 2)
				{
					AddGP(num);
				}
				SendHideMessage($"Seus {num} pontos da aventura bugou foram convertidos em {type}");
				m_character.Score = 0;
			}
			if (num2 > 0 && m_character.damageScores > 0)
			{
				if (num3 == 1)
				{
					AddOffer(num2);
				}
				if (num3 == 2)
				{
					AddGP(num2);
				}
				SendHideMessage($"Seus {num2} pontos do boss foram convertidos em {type}");
				m_character.damageScores = 0;
			}
			UpdateProperties();
		}

		public bool EquipItem(ItemInfo item, int place)
		{
			if (!item.CanEquip() || item.BagType != m_equipBag.BagType)
			{
				return false;
			}
			int num = m_equipBag.FindItemEpuipSlot(item.Template);
			int num2 = num;
			bool flag = (uint)(num2 - 9) <= 1u;
			bool flag2 = flag;
			if (flag2)
			{
				bool flag3 = false;
				bool flag4 = false;
				bool flag5 = false;
				if (1 == 0)
				{
				}
				int num3;
				switch (place)
				{
				case 10:
					num3 = 0;
					break;
				case 9:
					num3 = 0;
					break;
				default:
					num3 = 1;
					break;
				}
				if (1 == 0)
				{
				}
				int num4 = num3;
				bool flag6 = false;
				bool flag7 = false;
				bool flag8 = false;
				flag2 = num4 == 0;
			}
			if (flag2)
			{
				num = place;
			}
			else if ((num == 7 || num == 8) && (place == 7 || place == 8))
			{
				num = place;
			}
			return m_equipBag.MoveItem(item.Place, num, item.Count);
		}

		public void OnLevelUp(int grade)
		{
			if (this.LevelUp != null)
			{
				this.LevelUp(this);
			}
		}

		public void OnUsingItem(int templateID, int count = 1)
		{
			if (this.AfterUsingItem != null)
			{
				this.AfterUsingItem(templateID, count);
			}
		}

		public void SendMessagem(string nickname, bool win, string ConsortiaName)
		{
			WorldMgr.SendSysNotice("O jogadores [" + nickname + "] Venceu a Sociedade " + ConsortiaName);
		}

		public void OnGameOver(AbstractGame game, bool isWin, int gainXp, int TotalPlayerCount)
		{
			int num1 = 1;
			if (game.RoomType == eRoomType.Match)
			{
				if (isWin)
				{
					m_character.Win++;
				}
				m_character.Total++;
			}
			if (game.GameType == eGameType.Free && game.RoomType == eRoomType.Match && isWin)
			{
				Extra.UpdateEventCondition(2, num1, isPlus: true);
			}
			if (game.GameType == eGameType.Guild)
			{
				if (isWin)
				{
					Extra.UpdateEventCondition(7, num1, isPlus: true);
				}
				EveryDayActivePointMgr.ChangeUserActiveData(this, 23, 1);
			}
			this.GameOver?.Invoke(game, isWin, gainXp);
			if (TotalPlayerCount % 2 == 0)
			{
				this.GameOverTeamCount?.Invoke(game, isWin, gainXp, TotalPlayerCount / 2);
			}
			if (m_character.IsMarried && m_currentRoom.GetPlayers().Any((GamePlayer s) => s.PlayerCharacter.ID == m_character.SpouseID))
			{
				this.OnMarryMatchIsOver?.Invoke(game, isWin, gainXp);
			}
			eRoomType roomType = game.RoomType;
			if (roomType == eRoomType.Dungeon && CurrentRoom != null)
			{
				CurrentRoom.LevelLimits = (int)CurrentRoom.GetLevelLimit(this);
				CurrentRoom.MapId = 10000;
				CurrentRoom.HardLevel = eHardLevel.Normal;
				CurrentRoom.currentFloor = 0;
				CurrentRoom.isOpenBoss = false;
				CurrentRoom.SendRoomSetupChange(CurrentRoom);
			}
			EveryDayActivePointMgr.SetEverydayActiveChangeType(this, game.RoomType);
		}

		public void OnMissionOver(AbstractGame game, bool isWin, int missionId, int turnNum)
		{
			if (this.MissionOver != null)
			{
				this.MissionOver(game, missionId, isWin);
			}
			if (this.MissionTurnOver != null && isWin)
			{
				this.MissionTurnOver(game, missionId, turnNum);
			}
		}

		public void OnItemStrengthen(int categoryID, int level)
		{
			if (this.ItemStrengthen != null)
			{
				this.ItemStrengthen(categoryID, level);
			}
		}

		public void OnPaid(int money, int gold, int offer, int gifttoken, int medal, string payGoods)
		{
			if (this.Paid != null)
			{
				this.Paid(money, gold, offer, gifttoken, medal, payGoods);
			}
		}

		public void OnAdoptPetEvent()
		{
			if (this.AdoptPetEvent != null)
			{
				this.AdoptPetEvent();
			}
		}

		public void OnNewGearEvent(int CategoryID)
		{
			if (this.NewGearEvent != null)
			{
				this.NewGearEvent(CategoryID);
			}
		}

		public void OnUpLevelPetEvent(int templateId, int grade)
		{
			this.UpLevelPetEvent?.Invoke(templateId, grade);
		}

		public void OnCropPrimaryEvent()
		{
			if (this.CropPrimaryEvent != null)
			{
				this.CropPrimaryEvent();
			}
		}

		public void OnSeedFoodPetEvent()
		{
			if (this.SeedFoodPetEvent != null)
			{
				this.SeedFoodPetEvent();
			}
		}

		public void OnUserToemGemstoneEvent()
		{
			if (this.UserToemGemstonetEvent != null)
			{
				this.UserToemGemstonetEvent();
			}
		}

		public void OnUnknowQuestConditionEvent()
		{
			if (this.UnknowQuestConditionEvent != null)
			{
				this.UnknowQuestConditionEvent();
			}
		}

		public void OnItemInsert()
		{
			if (this.ItemInsert != null)
			{
				this.ItemInsert();
			}
		}

		public void OnItemFusion(int fusionType)
		{
			if (this.ItemFusion != null)
			{
				this.ItemFusion(fusionType);
			}
		}

		public void OnItemMelt(int categoryID)
		{
			if (this.ItemMelt != null)
			{
				this.ItemMelt(categoryID);
			}
		}

		public void OnKillingLiving(AbstractGame game, int type, int id, bool isLiving, int damage)
		{
			if (this.AfterKillingLiving != null)
			{
				this.AfterKillingLiving(game, type, id, isLiving, damage);
			}
			if (this.GameKillDrop != null && !isLiving)
			{
				this.GameKillDrop(game, type, id, isLiving);
			}
		}

		public void OnGuildChanged()
		{
			if (this.GuildChanged != null)
			{
				this.GuildChanged();
			}
		}

		public void OnItemCompose(int composeType)
		{
			if (this.ItemCompose != null)
			{
				this.ItemCompose(composeType);
			}
		}

		public void OnTimeOnline()
		{
			if (this.TimeOnline != null)
			{
				this.TimeOnline();
			}
		}

		public bool RemoveAllTemplate(int templateId, int count)
		{
			int itemCount = m_equipBag.GetItemCount(0, templateId);
			int itemCount2 = m_propBag.GetItemCount(templateId);
			int itemCount3 = m_ConsortiaBag.GetItemCount(templateId);
			int num = itemCount + itemCount2 + itemCount3;
			ItemTemplateInfo itemTemplateInfo = ItemMgr.FindItemTemplate(templateId);
			if (itemTemplateInfo != null && num >= count)
			{
				if (itemCount > 0 && count > 0 && RemoveTempate(eBageType.EquipBag, itemTemplateInfo, (itemCount > count) ? count : itemCount))
				{
					count = ((count >= itemCount) ? (count - itemCount) : 0);
				}
				if (itemCount2 > 0 && count > 0 && RemoveTempate(eBageType.PropBag, itemTemplateInfo, (itemCount2 > count) ? count : itemCount2))
				{
					count = ((count >= itemCount2) ? (count - itemCount2) : 0);
				}
				if (itemCount3 > 0 && count > 0 && RemoveTempate(eBageType.Consortia, itemTemplateInfo, (itemCount3 > count) ? count : itemCount3))
				{
					count = ((count >= itemCount3) ? (count - itemCount3) : 0);
				}
				if (count == 0)
				{
					return true;
				}
				if (log.IsErrorEnabled)
				{
					log.Error($"Item Remove Error, PlayerId {m_playerId} Remove TemplateId {templateId} Is Not Zero!");
				}
			}
			return false;
		}

		public void DirectAddValue(SpecialItemDataInfo specialInfo)
		{
			if (specialInfo.Money > 0)
			{
				SendMessage(string.Format("{0} {1} {2}", LanguageMgr.GetTranslation("OpenUpArkHandler.Start"), specialInfo.Money, LanguageMgr.GetTranslation("OpenUpArkHandler.Money")));
				AddMoney(specialInfo.Money);
			}
			if (specialInfo.Gold > 0)
			{
				SendMessage(string.Format("{0} {1} {2}", LanguageMgr.GetTranslation("OpenUpArkHandler.Start"), specialInfo.Gold, LanguageMgr.GetTranslation("OpenUpArkHandler.Gold")));
				AddGold(specialInfo.Gold);
			}
			if (specialInfo.GiftToken > 0)
			{
				SendMessage(string.Format("{0} {1} {2}", LanguageMgr.GetTranslation("OpenUpArkHandler.Start"), specialInfo.GiftToken, LanguageMgr.GetTranslation("L.Cupons")));
				AddGiftToken(specialInfo.GiftToken);
			}
			if (specialInfo.GP > 0)
			{
				SendMessage(string.Format("{0} {1} {2}", LanguageMgr.GetTranslation("OpenUpArkHandler.Start"), specialInfo.GP, LanguageMgr.GetTranslation("OpenUpArkHandler.Gp")));
				AddGP(specialInfo.GP);
			}
			if (specialInfo.MyHonor > 0)
			{
				SendMessage(string.Format("{0} {1} {2}", LanguageMgr.GetTranslation("OpenUpArkHandler.Start"), specialInfo.MyHonor, LanguageMgr.GetTranslation("OpenUpArkHandler.honor")));
				AddHonor(specialInfo.MyHonor);
			}
			if (specialInfo.Medal > 0)
			{
				SendMessage(string.Format("{0} {1} {2}", LanguageMgr.GetTranslation("OpenUpArkHandler.Start"), specialInfo.Medal, LanguageMgr.GetTranslation("OpenUpArkHandler.Medal")));
				AddMedal(specialInfo.Medal);
			}
		}

		//SPA BY MOISES
        public HotSpringRoom CurrentHotSpringRoom
        {
            get => _hotSpringRoom0;
            set => _hotSpringRoom0 = value;
        }

        public void OnMoneyCharge(int money)
		{
			this.MoneyCharge?.Invoke(money);
		}

		public void ChargeToUser()
		{
			int money = 0;
			using (PlayerBussiness pb = new PlayerBussiness())
			{
				if (!pb.ChargeToUser(PlayerCharacter.UserName, ref money, PlayerCharacter.NickName) || money <= 0)
				{
					return;
				}
				string title = "Carregamento sucedido.";
				string content = $"Prezado jogador. Você já recarregou {money} cupons na sua conta.";
				EveryDayActivePointMgr.ChangeUserActiveData(this, 61, money);
				SendMailToUser(pb, content, title, eMailType.Manage);
				AddMoney(money);
				OnMoneyCharge(money);
				Out.SendMailResponse(PlayerCharacter.ID, eMailRespose.Receiver);
				Extra.UpdateEventCondition(5, money, isPlus: true);
				Extra.UpdateEventCondition(6, money, isPlus: true);
				AddHappyRechargeLottery(money);
				if (Extra.Info.LeftRoutteRate > 0.0)
				{
					int Rate = (int)((double)((float)money / 10f) * Extra.Info.LeftRoutteRate);
					int value = Math.Min(Rate, 5000);
					if (value > 0)
					{
						SendMoneyMailToUser(LanguageMgr.GetTranslation("GameServer.LeftRotterMail.Title"), LanguageMgr.GetTranslation("GameServer.LeftRotterMail.Content", value), value, eMailType.BuyItem);
					}
				}
			}
		}

		public void WebWarPass()
		{
			ForcesWarPass.ActivateWarPass();
			SendMessage("passe comprado com sucesso!");
		}

		public void AddHappyRechargeLottery(int rechargeAmount)
		{
			if (Game.Server.EventSystem.EventSystem.GetEvent<HappyRecharge>().IsOpen)
			{
				int baseCount = GameProperties.HappyChargeTokenPrice;
				int lotteryCount = 0;
				if (rechargeAmount >= baseCount)
				{
					lotteryCount = rechargeAmount / baseCount;
				}
				Extra.Info.HappyChargeLotteryCount += lotteryCount;
				Game.Server.EventSystem.EventSystem.GetEvent<HappyRecharge>().HandleHappyRechargeUpdate(this, 2);
			}
		}

		public bool PlayerHasLevel(int level)
		{
			bool hasLevel = PlayerCharacter.Grade >= level;
			if (!hasLevel)
			{
				SendMessage($"Necessário nível {level} para acessar este recurso");
			}
			return hasLevel;
		}

		public void OnItemStrengthenNew(int templateId, int categoryId, int level)
		{
			this.ItemStrengthenNew?.Invoke(templateId, categoryId, level);
		}

		private void NotifyConsumeMoney(int money)
		{
			this.MoneyConsume?.Invoke(money);
		}

		private void OnTotemLevelUp(int grade)
		{
			this.UpTotemLevelEvent?.Invoke(grade);
		}

		public void OnFigSpiritLevelUp(int grade)
		{
			this.UpFigSpiritLevelEvent?.Invoke(grade);
		}

		public void OnCardUpdateEvent(int templateId, int newLevel)
		{
			this.CardUpdateEvent?.Invoke(templateId, newLevel);
		}

		public void OnPropertiesChange()
		{
			this.PropertiesChange?.Invoke(PlayerCharacter);
		}

		public void OnPlayerVipLevelUpEvent(int level)
		{
			this.PlayerVipLevelUpEvent?.Invoke(level);
		}

		public void MasterApprenticeshipAdd()
		{
			if (this.MasterApprenticeship != null)
			{
				this.MasterApprenticeship();
			}
		}

		public int RemoveExScore(int value)
		{
			if (value > 0)
			{
				if (value >= ForcesWarPass.Data.ExScore)
				{
					value = ForcesWarPass.Data.ExScore;
				}
				ForcesWarPass.Data.ExScore -= value;
				Out.SendUpdateCoinInfo(ForcesWarPass.Data.ExScore);
				return value;
			}
			return 0;
		}
	}
}
