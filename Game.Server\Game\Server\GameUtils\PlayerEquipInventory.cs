using System;
using System.Collections.Generic;
using System.Linq;
using Bussiness;
using Bussiness.Managers;
using Game.Logic;
using Game.Server.EventSystem;
using Game.Server.GameObjects;
using Game.Server.Managers;
using SqlDataProvider.Data;

namespace Game.Server.GameUtils
{
	public class PlayerEquipInventory : PlayerInventory
	{
		private const int END_DRESS_BAG = 395;

		private const int END_EQUIP_BAG = 127;

		private const int START_DRESS_BAG = 128;

		private const int START_EQUIP_BAG = 31;

		private static readonly int[] StyleIndex = new int[15]
		{
			1, 2, 3, 4, 5, 6, 11, 13, 14, 15,
			16, 17, 18, 19, 20
		};

		public int SlotAvatarBegin => 128;

		public int SlotAvatarEnd => 395;

		public int SlotEquipEnd => 127;

		public int SlotEquipStart => 31;

		public PlayerEquipInventory(GamePlayer player)
			: base(player, saveTodb: true, 395, 0, 31, autoStack: true)
		{
		}

		public void AddBaseGemstoneProperty(ItemInfo item, ref int attack, ref int defence, ref int agility, ref int lucky, ref int hp)
		{
			foreach (UserGemStone current in m_player.GemStone)
			{
				int figSpiritId = current.FigSpiritId;
				int lv = Convert.ToInt32(current.FigSpiritIdValue.Split('|')[0].Split(',')[0]);
				int num = current.FigSpiritIdValue.Split('|').Length;
				int place = item.Place;
				int place2 = item.Place;
				switch (place2)
				{
				case 2:
					attack += FightSpiritTemplateMgr.getProp(figSpiritId, lv, place) * num;
					continue;
				case 3:
					lucky += FightSpiritTemplateMgr.getProp(figSpiritId, lv, place) * num;
					continue;
				case 5:
					agility += FightSpiritTemplateMgr.getProp(figSpiritId, lv, place) * num;
					continue;
				case 4:
					continue;
				}
				switch (place2)
				{
				case 11:
					defence += FightSpiritTemplateMgr.getProp(figSpiritId, lv, place) * num;
					break;
				case 13:
					hp += FightSpiritTemplateMgr.getProp(figSpiritId, lv, place) * num;
					break;
				}
			}
		}

		public void AddBaseLatentProperty(ItemInfo item, ref int attack, ref int defence, ref int agility, ref int lucky)
		{
			if (item != null && !item.IsValidLatentEnergy())
			{
				string[] array = item.latentEnergyCurStr.Split(',');
				attack += Convert.ToInt32(array[0]);
				defence += Convert.ToInt32(array[1]);
				agility += Convert.ToInt32(array[2]);
				lucky += Convert.ToInt32(array[3]);
			}
		}

		public void AddBaseTotemProperty(PlayerInfo p, ref int attack, ref int defence, ref int agility, ref int lucky, ref int hp)
		{
			attack += TotemMgr.GetTotemProp(p.totemId, "att");
			defence += TotemMgr.GetTotemProp(p.totemId, "def");
			agility += TotemMgr.GetTotemProp(p.totemId, "agi");
			lucky += TotemMgr.GetTotemProp(p.totemId, "luc");
			hp += TotemMgr.GetTotemProp(p.totemId, "blo");
		}

		public void AddBeadProperty(int place, ref int attack, ref int defence, ref int agility, ref int lucky, ref int hp)
		{
			ItemInfo itemAt = m_player.BeadBag.GetItemAt(place);
			if (itemAt != null)
			{
				AddRuneProperty(itemAt, ref attack, ref defence, ref agility, ref lucky, ref hp);
			}
		}

		public override bool AddItem(ItemInfo item)
		{
			if (Equip.isAvatar(item.Template))
			{
				return base.AddItem(item, 128, 395);
			}
			return base.AddItem(item, 31, 127);
		}

		public override bool AddItemTo(ItemInfo item, int place)
		{
			if (Equip.isAvatar(item.Template) && !IsEquipSlot(place))
			{
				place = FindFirstEmptySlot(128, 395);
			}
			if (base.AddItemTo(item, place))
			{
				item.UserID = m_player.PlayerCharacter.ID;
				item.IsExist = true;
				return true;
			}
			return false;
		}

		public void AddRuneProperty(ItemInfo item, ref int attack, ref int defence, ref int agility, ref int lucky, ref int hp)
		{
			RuneTemplateInfo runeTemplateInfo = RuneMgr.FindRuneByTemplateID(item.TemplateID);
			if (runeTemplateInfo == null)
			{
				return;
			}
			string[] array = runeTemplateInfo.Attribute1.Split('|');
			string[] array2 = runeTemplateInfo.Attribute2.Split('|');
			string[] array3 = null;
			if (runeTemplateInfo.Type3 == 37)
			{
				array3 = runeTemplateInfo.Attribute3.Split('|');
			}
			int num = 0;
			int num2 = 0;
			int num6 = 0;
			if (item.Hole1 > runeTemplateInfo.BaseLevel)
			{
				if (array.Length > 1)
				{
					num = 1;
				}
				if (array2.Length > 1)
				{
					num2 = 1;
				}
				if (runeTemplateInfo.Type3 == 37 && array3.Length > 1)
				{
					num6 = 1;
				}
			}
			int attribute1 = Convert.ToInt32(array[num]);
			int attribute2 = Convert.ToInt32(array2[num2]);
			if (runeTemplateInfo.Type3 != 37)
			{
				switch (runeTemplateInfo.Type1)
				{
				case 31:
					attack += attribute1;
					hp += attribute2;
					break;
				case 32:
					defence += attribute1;
					hp += attribute2;
					break;
				case 33:
					agility += attribute1;
					hp += attribute2;
					break;
				case 34:
					lucky += attribute1;
					hp += attribute2;
					break;
				case 35:
					hp += attribute2;
					break;
				case 36:
					hp += attribute2;
					break;
				case 37:
					hp += attribute1;
					break;
				}
			}
			else
			{
				int attribute3 = Convert.ToInt32(array3[num6]);
				switch (runeTemplateInfo.Type2)
				{
				case 31:
					attack += attribute2;
					break;
				case 32:
					defence += attribute2;
					break;
				case 33:
					agility += attribute2;
					break;
				case 34:
					lucky += attribute2;
					break;
				}
				int type = runeTemplateInfo.Type3;
				int num7 = type;
				if (num7 == 37)
				{
					hp += attribute3;
				}
			}
		}

		public ItemInfo[] GetEquipedItens()
		{
			List<ItemInfo> Itens = new List<ItemInfo>();
			for (int x = 0; x < 31; x++)
			{
				if (m_items[x] != null)
				{
					Itens.Add(m_items[x]);
				}
			}
			return Itens.ToArray();
		}

		public override bool AddTemplate(ItemInfo cloneItem, int count)
		{
			if (Equip.isAvatar(cloneItem.Template))
			{
				return base.AddTemplate(cloneItem, count, 128, 395);
			}
			return base.AddTemplate(cloneItem, count, 31, 127);
		}

		public bool CanEquipSlotContains(int slot, ItemTemplateInfo temp)
		{
			if (temp.CategoryID == 8 || temp.CategoryID == 28)
			{
				return slot == 7 || slot == 8;
			}
			if (temp.CategoryID == 9 || temp.CategoryID == 29)
			{
				if (Equip.isWeddingRing(temp))
				{
					return slot == 9 || slot == 10 || slot == 16;
				}
				return slot == 9 || slot == 10;
			}
			if (temp.CategoryID == 13)
			{
				return slot == 11;
			}
			if (temp.CategoryID == 14)
			{
				return slot == 12;
			}
			if (temp.CategoryID == 15)
			{
				return slot == 13;
			}
			if (temp.CategoryID == 16)
			{
				return slot == 14;
			}
			if (temp.CategoryID == 17 || temp.CategoryID == 31)
			{
				return slot == 15;
			}
			if (temp.CategoryID == 27)
			{
				return slot == 6;
			}
			if (temp.CategoryID == 40)
			{
				return slot == 17;
			}
			if (temp.CategoryID == 64)
			{
				return slot == 20;
			}
			return temp.CategoryID - 1 == slot;
		}

		public void EquipBuffer()
		{
			m_player.EquipEffect.Clear();
			for (int i = 0; i < 31; i++)
			{
				ItemInfo itemAt = m_player.BeadBag.GetItemAt(i);
				if (itemAt != null)
				{
					RuneTemplateInfo runeTemplateInfo = RuneMgr.FindRuneByTemplateID(itemAt.TemplateID);
					if (runeTemplateInfo != null && (runeTemplateInfo.Type1 == 37 || runeTemplateInfo.Type1 == 39 || runeTemplateInfo.Type1 < 31))
					{
						m_player.AddBeadEffect(itemAt);
					}
				}
			}
		}

		public int FindItemEpuipSlot(ItemTemplateInfo item)
		{
			switch (item.CategoryID)
			{
			case 13:
				return 11;
			case 14:
				return 12;
			case 15:
				return 13;
			case 16:
				return 14;
			case 27:
				return 6;
			case 40:
				return 17;
			case 8:
			case 28:
				if (m_items[7] == null)
				{
					return 7;
				}
				return 8;
			case 9:
			case 29:
				if (m_items[9] == null)
				{
					return 9;
				}
				return 10;
			case 17:
			case 31:
				return 15;
			default:
				return item.CategoryID - 1;
			}
		}

		public List<ItemInfo> GetAllEquipItems()
		{
			List<ItemInfo> list = new List<ItemInfo>();
			for (int i = 0; i < 31; i++)
			{
				ItemInfo itemInfo = m_items[i];
				if (itemInfo != null)
				{
					list.Add(itemInfo);
				}
			}
			return list;
		}

		public void GetUserNimbus()
		{
			int num = 0;
			int num2 = 0;
			for (int i = 0; i < 31; i++)
			{
				ItemInfo itemAt = GetItemAt(i);
				if (itemAt == null)
				{
					continue;
				}
				if (itemAt.IsGold)
				{
					if (itemAt.Template.CategoryID == 1 || itemAt.Template.CategoryID == 5)
					{
						num = ((num > 1) ? num : 5);
					}
					if (itemAt.Template.CategoryID == 7)
					{
						num2 = ((num2 > 1) ? num2 : 5);
					}
				}
				if (itemAt.StrengthenLevel >= 5 && itemAt.StrengthenLevel <= 8)
				{
					if (itemAt.Template.CategoryID == 1 || itemAt.Template.CategoryID == 5)
					{
						num = ((num <= 1) ? 1 : num);
					}
					if (itemAt.Template.CategoryID == 7)
					{
						num2 = ((num2 <= 1) ? 1 : num2);
					}
				}
				if (itemAt.StrengthenLevel >= 9 && itemAt.StrengthenLevel <= 11)
				{
					if (itemAt.Template.CategoryID == 1 || itemAt.Template.CategoryID == 5)
					{
						num = ((num > 1) ? num : 2);
					}
					if (itemAt.Template.CategoryID == 7)
					{
						num2 = ((num2 > 1) ? num2 : 2);
					}
				}
				if (itemAt.StrengthenLevel >= 12 && itemAt.StrengthenLevel <= 14)
				{
					if (itemAt.Template.CategoryID == 1 || itemAt.Template.CategoryID == 5)
					{
						num = ((num > 1) ? num : 3);
					}
					if (itemAt.Template.CategoryID == 7)
					{
						num2 = ((num2 > 1) ? num2 : 3);
					}
				}
				if (itemAt.StrengthenLevel >= 15)
				{
					if (itemAt.Template.CategoryID == 1 || itemAt.Template.CategoryID == 5)
					{
						num = ((num > 1) ? num : 5);
					}
					if (itemAt.Template.CategoryID == 7)
					{
						num2 = ((num2 > 1) ? num2 : 4);
					}
				}
				if (itemAt.IsGold)
				{
					if (itemAt.Template.CategoryID == 1 || itemAt.Template.CategoryID == 5)
					{
						num = ((num > 1) ? num : 5);
					}
					if (itemAt.Template.CategoryID == 27)
					{
						num2 = ((num2 > 1) ? num2 : 5);
					}
				}
				if (itemAt.StrengthenLevel >= 5 && itemAt.StrengthenLevel <= 8)
				{
					if (itemAt.Template.CategoryID == 1 || itemAt.Template.CategoryID == 5)
					{
						num = ((num <= 1) ? 1 : num);
					}
					if (itemAt.Template.CategoryID == 27)
					{
						num2 = ((num2 <= 1) ? 1 : num2);
					}
				}
				if (itemAt.StrengthenLevel >= 9 && itemAt.StrengthenLevel <= 11)
				{
					if (itemAt.Template.CategoryID == 1 || itemAt.Template.CategoryID == 5)
					{
						num = ((num > 1) ? num : 2);
					}
					if (itemAt.Template.CategoryID == 27)
					{
						num2 = ((num2 > 1) ? num2 : 2);
					}
				}
				if (itemAt.StrengthenLevel >= 12 && itemAt.StrengthenLevel <= 14)
				{
					if (itemAt.Template.CategoryID == 1 || itemAt.Template.CategoryID == 5)
					{
						num = ((num > 1) ? num : 3);
					}
					if (itemAt.Template.CategoryID == 27)
					{
						num2 = ((num2 > 1) ? num2 : 3);
					}
				}
				if (itemAt.StrengthenLevel >= 15)
				{
					if (itemAt.Template.CategoryID == 1 || itemAt.Template.CategoryID == 5)
					{
						num = ((num > 1) ? num : 4);
					}
					if (itemAt.Template.CategoryID == 27)
					{
						num2 = ((num2 > 1) ? num2 : 4);
					}
				}
			}
			m_player.PlayerCharacter.Nimbus = num * 100 + num2;
			m_player.Out.SendUpdatePublicPlayer(m_player.PlayerCharacter, m_player.BattleData.MatchInfo);
		}

		public override void LoadFromDatabase()
		{
			using (PlayerBussiness playerBussiness = new PlayerBussiness())
			{
				ItemInfo[] userBagByType = playerBussiness.GetUserBagByType(m_player.PlayerCharacter.ID, base.BagType);
				List<ItemInfo> item = new List<ItemInfo>();
				BeginChanges();
				try
				{
					ItemInfo[] array = userBagByType;
					foreach (ItemInfo itemInfo in array)
					{
						int Place = ((!IsWrongPlace(itemInfo)) ? itemInfo.Place : (Equip.isAvatar(itemInfo.Template) ? FindFirstEmptySlot(128, 395) : FindFirstEmptySlot(31, 127))); //MOCHILA AQUI
						if (Place == -1 || (!AddItemTo(itemInfo, Place) && !AddItem(itemInfo)))
						{
							item.Add(itemInfo);
						}
					}
				}
				finally
				{
					CommitChanges();
					if (item.Count > 0)
					{
						base.Player.BagFullSendToMail(item);
					}
				}
			}
		}

		public override bool MoveItem(int fromSlot, int toSlot, int count)
		{
			bool result;
			if (m_items[fromSlot] == null)
			{
				result = false;
			}
			else if (m_items[fromSlot].TemplateID != 10072 && m_items[fromSlot].TemplateID != 10077 && toSlot == 20)
			{
				result = false;
			}
			else if (m_items[fromSlot] != null && m_items[toSlot] != null && Equip.isAvatar(m_items[fromSlot].Template) && Equip.isAvatar(m_items[toSlot].Template) && m_items[toSlot].CanStackedTo(m_items[fromSlot]))
			{
				if (m_items[toSlot].ValidDate != 0)
				{
					if (m_items[fromSlot].ValidDate != 0)
					{
						m_items[toSlot].ValidDate += m_items[fromSlot].ValidDate;
					}
					else
					{
						m_items[toSlot].ValidDate = 0;
					}
				}
				RemoveItemAt(fromSlot);
				UpdateItem(m_items[toSlot]);
				result = true;
			}
			else
			{
				if (IsEquipSlot(fromSlot) && !IsEquipSlot(toSlot) && m_items[toSlot] != null && m_items[toSlot].Template.CategoryID != m_items[fromSlot].Template.CategoryID)
				{
					if (!CanEquipSlotContains(fromSlot, m_items[toSlot].Template))
					{
						toSlot = ((!Equip.isAvatar(m_items[fromSlot].Template)) ? FindFirstEmptySlot(31, 127) : FindFirstEmptySlot(128, 395));
					}
				}
				else
				{
					if (IsEquipSlot(toSlot))
					{
						if (!CanEquipSlotContains(toSlot, m_items[fromSlot].Template))
						{
							UpdateItem(m_items[fromSlot]);
							return false;
						}
						if (!m_player.CanEquip(m_items[fromSlot].Template) || !m_items[fromSlot].IsValidItem())
						{
							UpdateItem(m_items[fromSlot]);
							return false;
						}
					}
					if (IsEquipSlot(fromSlot) && m_items[toSlot] != null && !CanEquipSlotContains(fromSlot, m_items[toSlot].Template))
					{
						UpdateItem(m_items[toSlot]);
						return false;
					}
				}
				if (!Equip.isAvatar(m_items[fromSlot].Template) && toSlot > 127)
				{
					toSlot = FindFirstEmptySlot(31, 127);
				}
				else if (Equip.isAvatar(m_items[fromSlot].Template) && !IsEquipSlot(toSlot))
				{
					toSlot = FindFirstEmptySlot(128, 395);
				}
				result = base.MoveItem(fromSlot, toSlot, count);
			}
			return result;
		}

		public Dictionary<string, int> GetProp(int attack, int defence, int agility, int lucky, int hp)
		{
			return new Dictionary<string, int>
			{
				{ "Attack", attack },
				{ "Defence", defence },
				{ "Agility", agility },
				{ "Luck", lucky },
				{ "HP", hp }
			};
		}

		public override void UpdateChangedPlaces()
		{
			int[] array = m_changedPlaces.ToArray();
			bool flag = false;
			int[] array2 = array;
			foreach (int slot in array2)
			{
				if (!IsEquipSlot(slot))
				{
					continue;
				}
				ItemInfo itemAt = GetItemAt(slot);
				if (itemAt != null)
				{
					m_player.OnUsingItem(itemAt.TemplateID);
					itemAt.IsBinds = true;
					if (!itemAt.IsUsed)
					{
						itemAt.IsUsed = true;
						itemAt.BeginDate = DateTime.Now;
					}
				}
				flag = true;
				break;
			}
			base.UpdateChangedPlaces();
			if (flag)
			{
				UpdatePlayerProperties();
			}
		}

		public void AddDressStats(ref int attack, ref int defence, ref int agility, ref int lucky, ref int hp)
		{
			// Chỉ tính stats từ functional dress slot (slot 0)
			List<UserDressModelInfo> functionalDressItems = m_player.DressModel.GetFunctionalDressModel();
			foreach (UserDressModelInfo dressItem in functionalDressItems)
			{
				// Tìm item trong inventory dựa trên ItemID
				ItemInfo item = m_player.PropBag.GetItemByItemID(0, dressItem.ItemID);
				if (item != null && item.IsValidItem())
				{
					attack += item.Attack;
					defence += item.Defence;
					agility += item.Agility;
					lucky += item.Luck;
					hp += item.Property8; // HP property

					// Thêm latent energy nếu có
					AddBaseLatentProperty(item, ref attack, ref defence, ref agility, ref lucky);
				}
			}
		}

		public string GetDressStyle()
		{
			// Tạo style string từ cosmetic dress items (slot 1 hoặc 2)
			List<UserDressModelInfo> visualDressItems = m_player.DressModel.GetVisualDressModel();

			// Bắt đầu với style mặc định
			string baseStyle = m_player.PlayerCharacter.Sex ? "1101,2101,3101,4101,5101,6101,7001,13101,15001" : "1201,2201,3201,4201,5201,6201,7002,13201,15001";
			string[] styleParts = baseStyle.Split(',');

			// Override với dress items
			foreach (UserDressModelInfo dressItem in visualDressItems)
			{
				ItemInfo item = m_player.PropBag.GetItemByItemID(0, dressItem.ItemID);
				if (item != null)
				{
					int styleIndex = GetStyleIndexByCategoryID(dressItem.CategoryID);
					if (styleIndex >= 0 && styleIndex < styleParts.Length)
					{
						styleParts[styleIndex] = item.TemplateID + "|" + item.Template.Pic;
					}
				}
			}

			return string.Join(",", styleParts);
		}

		private int GetStyleIndexByCategoryID(int categoryID)
		{
			// Map CategoryID to style index
			switch (categoryID)
			{
				case 1: return 0; // HEAD
				case 2: return 1; // GLASS
				case 3: return 2; // HAIR
				case 4: return 3; // EFF
				case 5: return 4; // CLOTH
				case 6: return 5; // FACE
				case 13: return 7; // SUITS
				case 15: return 8; // WING
				default: return -1;
			}
		}

		public void UpdatePlayerProperties()
		{
			m_player.BeginChanges();
			try
			{
				int attack = 0;
				int defence = 0;
				int agility = 0;
				int lucky = 0;
				int num = 0;
				int num2 = 0;
				int hp = 0;
				int num3 = 0;
				string text = "";
				string text2 = "";
				string skin = "";
				int attack2 = 0;
				int defence2 = 0;
				int agility2 = 0;
				int lucky2 = 0;
				int hp2 = 0;
				int num4 = 0;
				int num5 = 0;
				int num6 = 0;
				int num7 = 0;
				int num8 = 0;
				int num9 = 0;
				int num10 = 0;
				int num11 = 0;
				int num12 = 0;
				int num13 = 0;
				int num14 = 0;
				int num15 = 0;
				int num16 = 0;
				int num17 = 0;
				int attack3 = 0;
				int defence3 = 0;
				int agility3 = 0;
				int lucky3 = 0;
				int hp3 = 0;
				int num18 = 0;
				int num19 = 0;
				int num20 = 0;
				int num21 = 0;
				int num22 = 0;
				int num23 = 0;
				int num24 = 0;
				int num25 = 0;
				int num26 = 0;
				int hp4 = 0;
				int num27 = 0;
				int num28 = 0;
				BaseAttributes EventSystemAttributes = new BaseAttributes();
				BaseAttributes AvatarColletionAttributes = new BaseAttributes();
				m_player.UpdatePet(m_player.PetBag.GetPetIsEquip());
				HomeTempPracticeInfo homeTempPracticeInfo = HomeTempleMgr.FindHomeTempPractice((m_player?.Temple?.CurrentLevel).GetValueOrDefault());
				List<UsersCardInfo> cards = m_player.CardBag.GetCards(0, 5);
				lock (m_lock)
				{
					text = ((m_items[0] == null) ? "" : (m_items[0].TemplateID + "|" + m_items[0].Template.Pic));
					text2 = ((m_items[0] == null) ? "" : m_items[0].Color);
					skin = ((m_items[5] == null) ? "" : m_items[5].Skin);
					for (int i = 0; i < 31; i++)
					{
						ItemInfo itemInfo = m_items[i];
						if (itemInfo != null)
						{
							if (itemInfo.itemInfoExtend != null && itemInfo.IsCanMagic(itemInfo.Template))
							{
								int property = itemInfo.Template.Property1;
								int num29 = property;
								if (num29 >= 4)
								{
									if (num29 >= 5)
									{
										num27 += itemInfo.itemInfoExtend.MagicAttack;
										num28 += itemInfo.itemInfoExtend.MagicDefence;
									}
									else
									{
										num27 += (int)((double)itemInfo.itemInfoExtend.MagicAttack * 0.8);
										num28 += (int)((double)itemInfo.itemInfoExtend.MagicDefence * 0.8);
									}
								}
								else if (num29 < 3)
								{
									if ((uint)num29 <= 2u)
									{
										num27 += itemInfo.itemInfoExtend.MagicAttack;
										num28 += itemInfo.itemInfoExtend.MagicDefence;
									}
								}
								else
								{
									num27 += (int)((double)itemInfo.itemInfoExtend.MagicAttack * 0.6);
									num28 += (int)((double)itemInfo.itemInfoExtend.MagicDefence * 0.6);
								}
							}
							attack += itemInfo.Attack;
							defence += itemInfo.Defence;
							agility += itemInfo.Agility;
							lucky += itemInfo.Luck;
							num3 = ((num3 > itemInfo.StrengthenLevel) ? num3 : itemInfo.StrengthenLevel);
							AddBaseLatentProperty(itemInfo, ref attack, ref defence, ref agility, ref lucky);
							AddBaseGemstoneProperty(itemInfo, ref attack2, ref defence2, ref agility2, ref lucky2, ref hp2);
							SubActiveConditionInfo subActiveInfo = SubActiveMgr.GetSubActiveInfo(itemInfo);
							if (subActiveInfo != null)
							{
								attack += subActiveInfo.GetValue("1");
								defence += subActiveInfo.GetValue("2");
								agility += subActiveInfo.GetValue("3");
								lucky += subActiveInfo.GetValue("4");
								hp += subActiveInfo.GetValue("5");
							}
							if (itemInfo.Template.CategoryID == 40)
							{
								m_player.PlayerCharacter.pvpBadgeID = itemInfo.TemplateID;
							}
						}
						AddBeadProperty(i, ref attack3, ref defence3, ref agility3, ref lucky3, ref hp3);
					}
					AddBaseTotemProperty(m_player.PlayerCharacter, ref attack, ref defence, ref agility, ref lucky, ref hp);
					if (m_player.Pet != null)
					{
						int num30 = 0;
						int num31 = 0;
						int num32 = 0;
						int num33 = 0;
						int num34 = 0;
						if (m_player.PlayerCharacter.evolutionGrade > 0)
						{
							PetFightPropertyInfo petFightProperty = PetMgr.GetPetFightProperty(m_player.PlayerCharacter.evolutionGrade);
							if (petFightProperty != null)
							{
								num32 = petFightProperty.Agility;
								num30 = petFightProperty.Attack;
								num31 = petFightProperty.Defence;
								num33 = petFightProperty.Lucky;
								num34 = petFightProperty.Blood;
							}
						}
						if (m_player.Pet.EquipList != null)
						{
							foreach (PetEquipDataInfo petEquipDataInfo in m_player.Pet.EquipList.Where((PetEquipDataInfo Equip) => Equip == null || Equip.eqTemplateID > 0 || 1 == 0))
							{
								num5 = petEquipDataInfo.eqType;
								switch (num5)
								{
								case 0:
								{
									PetMoeProperty petMoeProperty4 = PetExtraMgr.FindPetMoeProperty(base.Player.PlayerCharacter.EatPetInfo.WeaponLevel);
									if (petMoeProperty4 != null)
									{
										num18 += petMoeProperty4.Attack;
										num21 += petMoeProperty4.Lucky;
									}
									break;
								}
								case 1:
								{
									PetMoeProperty petMoeProperty5 = PetExtraMgr.FindPetMoeProperty(base.Player.PlayerCharacter.EatPetInfo.HatLevel);
									if (petMoeProperty5 != null)
									{
										num19 += petMoeProperty5.Defence;
									}
									break;
								}
								case 2:
								{
									PetMoeProperty petMoeProperty3 = PetExtraMgr.FindPetMoeProperty(base.Player.PlayerCharacter.EatPetInfo.ClothesLevel);
									if (petMoeProperty3 != null)
									{
										num20 += petMoeProperty3.Agility;
										num22 += petMoeProperty3.Blood;
									}
									break;
								}
								}
							}
						}
						num8 += m_player.Pet.TotalAttack + num30;
						num9 += m_player.Pet.TotalDefence + num31;
						num10 += m_player.Pet.TotalAgility + num32;
						num11 += m_player.Pet.TotalLuck + num33;
						num12 += m_player.Pet.TotalBlood + num34;
					}
					UserRankInfo rank = m_player.Rank.GetRank(m_player.PlayerCharacter.Honor);
					if (rank != null)
					{
						attack += rank.Attack;
						defence += rank.Defence;
						agility += rank.Agility;
						lucky += rank.Luck;
						hp += rank.HP;
					}
					AvatarColletionAttributes += m_player.AvatarCollectionBag.GetAttributes();
					attack += AvatarColletionAttributes.Attack;
					defence += AvatarColletionAttributes.Defence;
					agility += AvatarColletionAttributes.Agility;
					lucky += AvatarColletionAttributes.Luck;
					hp += AvatarColletionAttributes.Blood;
					List<ItemInfo> items = m_player.MagicStoneBag.GetItems(0, 30);
					foreach (ItemInfo item3 in items.Where((ItemInfo item2) => Equip.isMagicStone(item2.Template) && IsMagicStoneEquipSlot(item2.Place)))
					{
						num23 += item3.AttackCompose;
						num24 += item3.DefendCompose;
						num26 += item3.LuckCompose;
						num25 += item3.AgilityCompose;
						num27 += item3.itemInfoExtend.MagicAttack;
						num28 += item3.itemInfoExtend.MagicDefence;
					}
					foreach (UsersCardInfo item4 in cards)
					{
						num4 += CardMgr.GetProp(item4, 0);
						num5 += CardMgr.GetProp(item4, 1);
						num6 += CardMgr.GetProp(item4, 2);
						num7 += CardMgr.GetProp(item4, 3);
						if (item4.CardID > 0)
						{
							num4 += item4.Attack;
							num5 += item4.Defence;
							num6 += item4.Agility;
							num7 += item4.Luck;
						}
						if (item4.TemplateID > 0)
						{
							CardTemplateInfo cardTemplateInfo = CardMgr.FindCardTemplate(item4.TemplateID, item4.CardType);
							if (cardTemplateInfo != null)
							{
								num4 += cardTemplateInfo.AddAttack;
								num5 += cardTemplateInfo.AddDefend;
								num6 += cardTemplateInfo.AddAgility;
								num7 += cardTemplateInfo.AddLucky;
							}
						}
					}
					num13 += ExerciseMgr.GetExercise(m_player.PlayerCharacter.Texp.attTexpExp, "A");
					num14 += ExerciseMgr.GetExercise(m_player.PlayerCharacter.Texp.defTexpExp, "D");
					num15 += ExerciseMgr.GetExercise(m_player.PlayerCharacter.Texp.spdTexpExp, "AG");
					num16 += ExerciseMgr.GetExercise(m_player.PlayerCharacter.Texp.lukTexpExp, "L");
					num17 += ExerciseMgr.GetExercise(m_player.PlayerCharacter.Texp.hpTexpExp, "H");
					for (int j = 0; j < StyleIndex.Length; j++)
					{
						text += ",";
						text2 += ",";
						if (m_items[StyleIndex[j]] != null)
						{
							object obj = text;
							text = string.Concat(obj, m_items[StyleIndex[j]].TemplateID, "|", m_items[StyleIndex[j]].Pic);
							text2 += m_items[StyleIndex[j]].Color;
						}
					}
					foreach (UserRankInfo item5 in m_player.Rank.GetRank())
					{
						NewTitleTemplateInfo title = NewTitleManager.GetTitle(item5.TitleID);
						if (title != null)
						{
							attack += title.Att;
							defence += title.Def;
							agility += title.Agi;
							lucky += title.Luck;
						}
					}
					EquipBuffer();
				}
				EventSystemAttributes += Game.Server.EventSystem.EventSystem.GetAttributes(m_player);

				// Thêm stats từ functional dress slot (slot 0)
				AddDressStats(ref attack, ref defence, ref agility, ref lucky, ref hp);

				attack += attack2 + num4 + num8 + num13 + attack3 + num18 + num23 + homeTempPracticeInfo.Attack + EventSystemAttributes.Attack;
				defence += defence2 + num5 + num9 + num14 + defence3 + num19 + num24 + homeTempPracticeInfo.Defence + EventSystemAttributes.Defence;
				agility += agility2 + num6 + num10 + num15 + agility3 + num20 + num25 + homeTempPracticeInfo.Agility + EventSystemAttributes.Agility;
				lucky += lucky2 + num7 + num11 + num16 + lucky3 + num21 + num26 + homeTempPracticeInfo.Luck + EventSystemAttributes.Luck;
				num += num27;
				num2 += num28;
				SetsBuildTempMgr.GetSetsBuildProp(m_player.PlayerCharacter.fineSuitExp, ref defence2, ref hp, ref agility, ref num2, ref defence);
				hp += hp2 + num12 + num17 + hp3 + num22 + homeTempPracticeInfo.Blood + EventSystemAttributes.Blood;
				m_player.UpdateBaseProperties(attack, defence, agility, lucky, num, num2, hp);

				// Cập nhật style từ cosmetic dress items thay vì equipment
				string dressStyle = GetDressStyle();
				m_player.UpdateStyle(dressStyle, text2, skin);
				m_player.ApertureEquip(num3);
				m_player.UpdateWeapon(m_items[6]);
				m_player.UpdateSecondWeapon(m_items[15]);
				m_player.UpdateReduceDame(m_items[17]);
				m_player.UpdateHealstone(m_items[18]);
				m_player.PlayerProp.CreateProp(isSelf: true, "Texp", num13, num14, num15, num16, num17);
				m_player.PlayerProp.CreateProp(isSelf: true, "Card", num4, num5, num6, num7, 0);
				m_player.PlayerProp.CreateProp(isSelf: true, "Pet", num8, num9, num10, num11, num12);
				m_player.PlayerProp.CreateProp(isSelf: true, "Gem", attack2, defence2, agility2, lucky2, hp2);
				m_player.PlayerProp.CreateProp(isSelf: true, "Bead", attack3, defence3, agility3, lucky3, hp3);
				m_player.PlayerProp.CreateProp(isSelf: true, "Avatar", num18, num19, num20, num21, num22);
				m_player.PlayerProp.CreateProp(isSelf: true, "MagicStone", num23, num24, num25, num26, hp4);
				m_player.PlayerProp.UpadateBaseProp(isSelf: true, "MagicAttack", "MagicStone", num27);
				m_player.PlayerProp.UpadateBaseProp(isSelf: true, "MagicDefence", "MagicStone", num28);
				m_player.UpdateFightPower();
				GetUserNimbus();
				m_player.PlayerProp.ViewCurrent();
				m_player.OnPropertiesChange();
			}
			finally
			{
				m_player.CommitChanges();
			}
		}
	}
}
